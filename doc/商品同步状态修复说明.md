# 商品同步状态修复说明

## 问题描述

用户反馈：`php think hybrid:sync sync --mode=database --type=goods --batch-size=50` 命令在同步商品时，有些商品已经在银枣数据库中下架了，但在本地系统中仍然显示为 `normal`（上架）状态。

## 问题分析

### 根本原因

在 `application/common/service/HybridSyncService.php` 中的 `syncGoodsFromDatabase()` 方法中，查询条件限制了只同步 `gm.status = 1`（上架）的商品，导致下架商品（`gm.status = 0`）不会被同步，因此本地系统无法获取到这些商品的状态变更。

### 具体问题位置

1. **第855行**：`$where = ['gm.is_del' => 0, 'gm.status' => 1];`
2. **第871行**：`$countSql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0 AND gm.status = 1";`
3. **第875行**：`$sql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0 AND gm.status = 1))";`
4. **第965行**：`$sql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0 AND gm.status = 1";`
5. **第969行**：`$sql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0 AND gm.status = 1))";`

## 修复方案

### 修改内容

移除所有 `gm.status = 1` 的限制条件，允许同步所有未删除的商品（包括下架商品），以便正确更新商品状态。

### 具体修改

#### 1. 修改查询条件构建（第854-859行）

**修改前：**
```php
// 构建查询条件 - 使用goods_main作为主表
$where = ['gm.is_del' => 0, 'gm.status' => 1];
if ($traderId) {
    $where['gm.trader_id'] = $traderId;
}
```

**修改后：**
```php
// 构建查询条件 - 使用goods_main作为主表
// 修改：移除 status = 1 限制，同步所有商品（包括下架商品）以更新状态
$where = ['gm.is_del' => 0];
if ($traderId) {
    $where['gm.trader_id'] = $traderId;
}
```

#### 2. 修改计数查询条件（第867-879行）

**修改前：**
```php
if ($traderId) {
    // 针对特定trader_id使用不同的查询条件
    if ($traderId == 27500) {
        $countSql .= " AND gm.trader_id = {$traderId}";
    } else {
        $countSql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0 AND gm.status = 1";
    }
} else {
    // 全量同步时，需要包含特殊trader_id的商品
    $countSql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0 AND gm.status = 1))";
}
```

**修改后：**
```php
if ($traderId) {
    // 针对特定trader_id使用不同的查询条件
    if ($traderId == 27500) {
        $countSql .= " AND gm.trader_id = {$traderId}";
    } else {
        // 修改：移除 status = 1 限制，同步所有商品状态
        $countSql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0";
    }
} else {
    // 全量同步时，需要包含特殊trader_id的商品
    // 修改：移除 status = 1 限制，同步所有商品状态
    $countSql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0))";
}
```

#### 3. 修改数据查询条件（第960-972行）

**修改前：**
```php
if ($traderId) {
    // 针对特定trader_id使用不同的查询条件
    if ($traderId == 27500) {
        $sql .= " AND gm.trader_id = {$traderId}";
    } else {
        $sql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0 AND gm.status = 1";
    }
} else {
    // 全量同步时，需要包含特殊trader_id的商品
    $sql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0 AND gm.status = 1))";
}
```

**修改后：**
```php
if ($traderId) {
    // 针对特定trader_id使用不同的查询条件
    if ($traderId == 27500) {
        $sql .= " AND gm.trader_id = {$traderId}";
    } else {
        // 修改：移除 status = 1 限制，同步所有商品状态
        $sql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0";
    }
} else {
    // 全量同步时，需要包含特殊trader_id的商品
    // 修改：移除 status = 1 限制，同步所有商品状态
    $sql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0))";
}
```

### 状态映射逻辑

`extend/inzayun/DatabaseTransformer.php` 中的状态映射逻辑保持不变，已经正确实现：

```php
protected function mapGoodsStatus($status)
{
    return intval($status) === 1 ? 'normal' : 'hidden';
}
```

- `status = 1` → `'normal'`（上架）
- `status = 0` → `'hidden'`（下架）

## 修复效果

### 修复前
- 只同步上架商品（`status = 1`）
- 下架商品状态无法更新
- 本地系统中下架商品仍显示为 `normal`

### 修复后
- 同步所有未删除商品（`is_del = 0`）
- 包括上架和下架商品
- 正确更新商品状态：上架 → `normal`，下架 → `hidden`

## 测试验证

### 测试命令
```bash
# 干运行测试
php think hybrid:sync sync --mode=database --type=goods --batch-size=50 --dry-run

# 实际同步
php think hybrid:sync sync --mode=database --type=goods --batch-size=50
```

### 验证步骤
1. 运行同步命令
2. 检查本地数据库中下架商品的状态是否正确更新为 `hidden`
3. 验证上架商品的状态仍然保持为 `normal`
4. 确认同步后的商品总数与银枣数据库一致

### 测试结果
- ✅ 已成功移除所有 `gm.status = 1` 限制
- ✅ 找到修改注释
- ✅ DatabaseTransformer.php 包含正确的状态映射逻辑
- ✅ 正确使用 status 字段进行状态映射

## 注意事项

1. **保持删除条件**：仍然保持 `gm.is_del = 0` 条件，排除已删除的商品
2. **特殊商户处理**：对 `trader_id = 27500` 的特殊处理逻辑保持不变
3. **向后兼容**：修改不影响现有的上架商品同步逻辑
4. **性能影响**：同步商品数量可能增加，但能确保数据一致性

## 相关文件

- `application/common/service/HybridSyncService.php` - 主要修改文件
- `extend/inzayun/DatabaseTransformer.php` - 状态映射逻辑
- `doc/商品同步状态修复说明.md` - 本文档

## 修复日期

2024年12月19日

## 修复人员

AI Assistant (Augment Agent)
