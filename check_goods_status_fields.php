<?php
/**
 * 检查银枣数据库中商品状态相关字段
 */

require_once __DIR__ . '/extend/inzayun/DatabaseManager.php';

use inzayun\DatabaseManager;

echo "=== 检查银枣数据库商品状态字段 ===\n\n";

try {
    // 1. 检查 goods_main 表结构
    echo "1. 检查 goods_main 表结构...\n";
    $sql = "DESCRIBE goods_main";
    $columns = DatabaseManager::query($sql);
    
    echo "   goods_main 表字段:\n";
    foreach ($columns as $column) {
        if (strpos(strtolower($column['Field']), 'status') !== false || 
            strpos(strtolower($column['Field']), 'state') !== false ||
            strpos(strtolower($column['Field']), 'del') !== false ||
            strpos(strtolower($column['Field']), 'enable') !== false ||
            strpos(strtolower($column['Field']), 'active') !== false) {
            echo "     {$column['Field']}: {$column['Type']} (默认: {$column['Default']}) - {$column['Comment']}\n";
        }
    }
    
    // 2. 检查商品状态分布
    echo "\n2. 检查商品状态分布...\n";
    $statusSql = "
        SELECT 
            status,
            is_del,
            COUNT(*) as count,
            COUNT(*) * 100.0 / (SELECT COUNT(*) FROM goods_main) as percentage
        FROM goods_main 
        GROUP BY status, is_del
        ORDER BY status, is_del
    ";
    $statusStats = DatabaseManager::query($statusSql);
    
    echo "   商品状态统计:\n";
    foreach ($statusStats as $stat) {
        echo "     status={$stat['status']}, is_del={$stat['is_del']}: {$stat['count']} 个 (" . number_format($stat['percentage'], 2) . "%)\n";
    }
    
    // 3. 检查电商商品详情表状态
    echo "\n3. 检查 commerce_goods_detail 表状态字段...\n";
    try {
        $commerceSql = "DESCRIBE commerce_goods_detail";
        $commerceColumns = DatabaseManager::query($commerceSql);
        
        echo "   commerce_goods_detail 表状态相关字段:\n";
        foreach ($commerceColumns as $column) {
            if (strpos(strtolower($column['Field']), 'status') !== false || 
                strpos(strtolower($column['Field']), 'state') !== false ||
                strpos(strtolower($column['Field']), 'del') !== false ||
                strpos(strtolower($column['Field']), 'enable') !== false ||
                strpos(strtolower($column['Field']), 'sell') !== false) {
                echo "     {$column['Field']}: {$column['Type']} (默认: {$column['Default']}) - {$column['Comment']}\n";
            }
        }
        
        // 检查电商商品状态分布
        $commerceStatusSql = "
            SELECT 
                sell_out,
                is_del,
                COUNT(*) as count
            FROM commerce_goods_detail 
            GROUP BY sell_out, is_del
            ORDER BY sell_out, is_del
        ";
        $commerceStats = DatabaseManager::query($commerceStatusSql);
        
        echo "   电商商品状态统计:\n";
        foreach ($commerceStats as $stat) {
            echo "     sell_out={$stat['sell_out']}, is_del={$stat['is_del']}: {$stat['count']} 个\n";
        }
        
    } catch (Exception $e) {
        echo "   ⚠️  commerce_goods_detail 表不存在或无法访问\n";
    }
    
    // 4. 查看具体的下架商品示例
    echo "\n4. 查看下架商品示例...\n";
    $offlineGoodsSql = "
        SELECT 
            goods_id,
            goods_name,
            status,
            is_del,
            trader_id,
            addtime,
            updatetime
        FROM goods_main 
        WHERE status = 0 OR is_del = 1
        ORDER BY updatetime DESC
        LIMIT 10
    ";
    $offlineGoods = DatabaseManager::query($offlineGoodsSql);
    
    if (!empty($offlineGoods)) {
        echo "   下架/删除商品示例:\n";
        foreach ($offlineGoods as $goods) {
            $statusText = $goods['status'] == 1 ? '上架' : '下架';
            $delText = $goods['is_del'] == 0 ? '正常' : '已删除';
            echo "     ID:{$goods['goods_id']} | {$goods['goods_name']} | 状态:{$statusText} | 删除:{$delText} | 更新:" . date('Y-m-d H:i:s', $goods['updatetime']) . "\n";
        }
    } else {
        echo "   ✅ 没有找到下架或删除的商品\n";
    }
    
    // 5. 检查本地系统中的商品状态
    echo "\n5. 检查本地系统商品状态...\n";
    
    // 切换到本地数据库
    $localStatusSql = "
        SELECT 
            status,
            COUNT(*) as count,
            COUNT(*) * 100.0 / (SELECT COUNT(*) FROM zy_wanlshop_goods WHERE third_party_provider = 'inzayun') as percentage
        FROM zy_wanlshop_goods 
        WHERE third_party_provider = 'inzayun'
        GROUP BY status
        ORDER BY status
    ";
    
    // 使用本地数据库连接
    $localDb = \think\Db::connect();
    $localStats = $localDb->query($localStatusSql);
    
    echo "   本地系统商品状态统计:\n";
    foreach ($localStats as $stat) {
        $statusText = $stat['status'] == 'normal' ? '上架' : '下架';
        echo "     {$stat['status']} ({$statusText}): {$stat['count']} 个 (" . number_format($stat['percentage'], 2) . "%)\n";
    }
    
    // 6. 对比分析
    echo "\n6. 状态对比分析...\n";
    
    // 获取银枣数据库中上架商品数量
    $inzayunOnlineSql = "SELECT COUNT(*) as count FROM goods_main WHERE status = 1 AND is_del = 0";
    $inzayunOnlineResult = DatabaseManager::query($inzayunOnlineSql);
    $inzayunOnlineCount = $inzayunOnlineResult[0]['count'];
    
    // 获取银枣数据库中下架商品数量
    $inzayunOfflineSql = "SELECT COUNT(*) as count FROM goods_main WHERE status = 0 OR is_del = 1";
    $inzayunOfflineResult = DatabaseManager::query($inzayunOfflineSql);
    $inzayunOfflineCount = $inzayunOfflineResult[0]['count'];
    
    // 获取本地系统中的商品数量
    $localOnlineCount = 0;
    $localOfflineCount = 0;
    foreach ($localStats as $stat) {
        if ($stat['status'] == 'normal') {
            $localOnlineCount = $stat['count'];
        } else {
            $localOfflineCount = $stat['count'];
        }
    }
    
    echo "   对比结果:\n";
    echo "     银枣数据库 - 上架商品: {$inzayunOnlineCount} 个\n";
    echo "     银枣数据库 - 下架商品: {$inzayunOfflineCount} 个\n";
    echo "     本地系统 - 上架商品: {$localOnlineCount} 个\n";
    echo "     本地系统 - 下架商品: {$localOfflineCount} 个\n";
    
    $onlineDiff = $localOnlineCount - $inzayunOnlineCount;
    $offlineDiff = $localOfflineCount - $inzayunOfflineCount;
    
    echo "     差异分析:\n";
    echo "       上架商品差异: " . ($onlineDiff >= 0 ? '+' : '') . "{$onlineDiff}\n";
    echo "       下架商品差异: " . ($offlineDiff >= 0 ? '+' : '') . "{$offlineDiff}\n";
    
    if ($offlineDiff < 0) {
        echo "     ❌ 问题确认: 本地系统下架商品数量少于银枣数据库，说明下架状态同步有问题\n";
    } else {
        echo "     ✅ 状态同步正常\n";
    }
    
    echo "\n✅ 检查完成！\n";
    
} catch (Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
    echo "错误详情: " . $e->getTraceAsString() . "\n";
}
