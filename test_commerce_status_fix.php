<?php
/**
 * 测试电商商品状态修复
 */

echo "=== 测试电商商品状态修复 ===\n\n";

// 模拟运行同步命令（干运行模式）
echo "1. 测试同步命令（干运行模式）...\n";

try {
    // 使用 ThinkPHP 命令行工具
    $command = "php think hybrid:sync sync --mode=database --type=goods --batch-size=5 --dry-run";
    echo "执行命令: {$command}\n";
    
    // 执行命令并捕获输出
    $output = [];
    $returnCode = 0;
    exec($command . " 2>&1", $output, $returnCode);
    
    echo "命令返回码: {$returnCode}\n";
    echo "命令输出:\n";
    foreach ($output as $line) {
        echo "  {$line}\n";
    }
    
    // 分析输出结果
    $foundCommerceStatus = false;
    $syncedCount = 0;
    
    foreach ($output as $line) {
        if (strpos($line, 'commerce_status') !== false || strpos($line, '电商状态') !== false) {
            $foundCommerceStatus = true;
        }
        if (preg_match('/同步了\s*(\d+)\s*个商品/', $line, $matches)) {
            $syncedCount = intval($matches[1]);
        }
    }
    
    echo "\n分析结果:\n";
    if ($foundCommerceStatus) {
        echo "  ✅ 检测到电商状态处理\n";
    } else {
        echo "  ⚠️  未检测到电商状态处理\n";
    }
    
    if ($syncedCount > 0) {
        echo "  ✅ 同步了 {$syncedCount} 个商品\n";
    } else {
        echo "  ⚠️  未检测到商品同步数量\n";
    }
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}

echo "\n2. 检查修改的代码文件...\n";

// 检查 HybridSyncService.php 的修改
$serviceFile = 'application/common/service/HybridSyncService.php';
if (file_exists($serviceFile)) {
    $content = file_get_contents($serviceFile);
    
    // 检查是否添加了 commerce_status 字段
    if (strpos($content, 'cgd.status as commerce_status') !== false) {
        echo "  ✅ HybridSyncService.php 已添加 commerce_status 字段\n";
    } else {
        echo "  ❌ HybridSyncService.php 缺少 commerce_status 字段\n";
    }
    
    // 检查是否添加了电商商品过滤条件
    if (strpos($content, 'cgd.commerce_goods_detail_id IS NOT NULL') !== false) {
        echo "  ✅ 已添加电商商品过滤条件\n";
    } else {
        echo "  ❌ 缺少电商商品过滤条件\n";
    }
    
    // 检查是否关联了 commerce_goods_detail 表
    if (strpos($content, 'LEFT JOIN commerce_goods_detail cgd') !== false) {
        echo "  ✅ 已关联 commerce_goods_detail 表\n";
    } else {
        echo "  ❌ 缺少 commerce_goods_detail 表关联\n";
    }
} else {
    echo "  ❌ 找不到 HybridSyncService.php 文件\n";
}

// 检查 DatabaseTransformer.php 的状态映射
$transformerFile = 'extend/inzayun/DatabaseTransformer.php';
if (file_exists($transformerFile)) {
    $content = file_get_contents($transformerFile);
    
    // 检查是否添加了新的状态映射方法
    if (strpos($content, 'mapCommerceGoodsStatus') !== false) {
        echo "  ✅ DatabaseTransformer.php 已添加电商状态映射方法\n";
    } else {
        echo "  ❌ DatabaseTransformer.php 缺少电商状态映射方法\n";
    }
    
    // 检查是否使用了 commerce_status 字段
    if (strpos($content, "mapCommerceGoodsStatus(\$goodsData['commerce_status']") !== false) {
        echo "  ✅ 正确使用 commerce_status 字段进行状态映射\n";
    } else {
        echo "  ❌ 状态映射可能有问题\n";
    }
} else {
    echo "  ❌ 找不到 DatabaseTransformer.php 文件\n";
}

echo "\n3. 生成修复总结...\n";

echo "修复内容总结:\n";
echo "  1. ✅ 修改 HybridSyncService.php 查询字段\n";
echo "     - 添加 cgd.status as commerce_status 字段\n";
echo "     - 关联 commerce_goods_detail 表并过滤已删除记录\n";
echo "     - 添加电商商品存在性检查\n";
echo "  2. ✅ 修改 DatabaseTransformer.php 状态映射\n";
echo "     - 添加 mapCommerceGoodsStatus() 方法\n";
echo "     - 使用 commerce_status 字段而不是 goods_main.status\n";
echo "     - 正确映射电商商品状态：1=上架(normal), 2=下架(hidden)\n";
echo "  3. ✅ 修改查询条件\n";
echo "     - 确保只同步有电商信息的商品\n";
echo "     - 包含上架和下架的电商商品\n";

echo "\n4. 状态映射规则:\n";
echo "  电商商品状态 (commerce_goods_detail.status):\n";
echo "    - 1 → 'normal' (上架)\n";
echo "    - 2 → 'hidden' (下架)\n";
echo "    - 其他值 → 'hidden' (默认下架)\n";

echo "\n5. 建议的测试步骤:\n";
echo "  1. 运行: php think hybrid:sync sync --mode=database --type=goods --batch-size=50\n";
echo "  2. 检查本地数据库中电商商品的状态是否正确\n";
echo "  3. 验证 commerce_goods_detail.status=1 的商品显示为 'normal'\n";
echo "  4. 验证 commerce_goods_detail.status=2 的商品显示为 'hidden'\n";
echo "  5. 确认只同步了有电商信息的商品\n";

echo "\n✅ 电商商品状态修复完成！现在同步命令应该能正确处理电商商品的上下架状态。\n";
