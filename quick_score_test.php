<?php
/**
 * 快速积分测试
 */

// 设置环境变量
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/base.php';

// 初始化应用
\think\App::initCommon();

use app\common\model\User;

echo "=== 快速积分同步测试 ===\n\n";

try {
    // 查找用户ID为689的用户（从日志中看到的）
    $user = User::get(689);
    
    if (!$user) {
        echo "❌ 未找到用户ID为689的用户\n";
        exit;
    }
    
    echo "用户信息:\n";
    echo "  ID: {$user->id}\n";
    echo "  手机: {$user->mobile}\n";
    echo "  银枣会员ID: {$user->third_party_member_id}\n";
    echo "  当前本地积分: {$user->getData('score')}\n\n";
    
    // 查询银枣数据库中的积分
    $inzayunPdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=dshop;charset=utf8mb4',
        'root',
        'sl331639',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $inzayunPdo->prepare("SELECT member_points FROM trader_member WHERE member_id = ? AND is_del = 0");
    $stmt->execute([$user->third_party_member_id]);
    $inzayunData = $stmt->fetch();
    
    echo "银枣数据库积分: {$inzayunData['member_points']}\n\n";
    
    echo "触发积分获取器...\n";
    
    // 触发积分获取器
    $score = $user->score;
    
    echo "获取器返回积分: {$score}\n";
    
    // 重新查询本地数据库
    $updatedUser = User::get(689);
    echo "更新后本地积分: {$updatedUser->getData('score')}\n";
    
    if ($score == $inzayunData['member_points']) {
        echo "✅ 积分同步成功！\n";
    } else {
        echo "❌ 积分同步失败\n";
        echo "  期望: {$inzayunData['member_points']}\n";
        echo "  实际: {$score}\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
