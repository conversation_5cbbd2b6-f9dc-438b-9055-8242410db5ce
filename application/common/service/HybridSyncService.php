<?php

namespace app\common\service;

use think\Exception;
use think\Db;
use inzayun\DatabaseManager;
use inzayun\DatabaseTransformer;

/**
 * 混合同步服务
 * 支持数据库直接同步和API同步
 */
class HybridSyncService
{
    /**
     * 数据转换器
     * @var DatabaseTransformer
     */
    protected $transformer;

    /**
     * 同步日志
     * @var array
     */
    protected $syncLog = [];

    public function __construct()
    {
        $this->transformer = new DatabaseTransformer();
    }

    /**
     * 测试连接
     * @return array
     */
    public function testConnections()
    {
        $results = [
            'success' => true,
            'details' => []
        ];

        // 测试本地数据库连接
        try {
            Db::query('SELECT 1');
            $results['details']['local_database'] = [
                'success' => true,
                'message' => '本地数据库连接正常'
            ];
        } catch (Exception $e) {
            $results['success'] = false;
            $results['details']['local_database'] = [
                'success' => false,
                'message' => '本地数据库连接失败: ' . $e->getMessage()
            ];
        }

        // 测试银枣数据库连接
        try {
            DatabaseManager::init();
            $results['details']['inzayun_database'] = [
                'success' => true,
                'message' => '银枣数据库连接正常'
            ];
        } catch (Exception $e) {
            $results['success'] = false;
            $results['details']['inzayun_database'] = [
                'success' => false,
                'message' => '银枣数据库连接失败: ' . $e->getMessage()
            ];
        }

        return $results;
    }

    /**
     * 获取同步状态
     * @return array
     */
    public function getSyncStatus()
    {
        $status = [
            'local_db' => false,
            'inzayun_db' => false,
            'stats' => [
                'shops' => 0,
                'categories' => 0,
                'goods' => 0
            ],
            'recent_syncs' => []
        ];

        try {
            // 检查本地数据库
            Db::query('SELECT 1');
            $status['local_db'] = true;

            // 统计本地数据
            $status['stats']['shops'] = Db::name('wanlshop_shop')
                ->where('third_party_provider', 'inzayun')
                ->count();
            $status['stats']['categories'] = Db::name('wanlshop_category')
                ->where('third_party_provider', 'inzayun')
                ->count();
            $status['stats']['goods'] = Db::name('wanlshop_goods')
                ->where('third_party_provider', 'inzayun')
                ->count();

        } catch (Exception $e) {
            // 本地数据库连接失败
        }

        try {
            // 检查银枣数据库
            DatabaseManager::init();
            $status['inzayun_db'] = true;
        } catch (Exception $e) {
            // 银枣数据库连接失败
        }

        // 获取最近同步记录
        try {
            $recentSyncs = Db::name('sync_log')
                ->where('sync_provider', 'inzayun')
                ->order('id DESC')
                ->limit(5)
                ->field('sync_type,status,createtime')
                ->select();

            foreach ($recentSyncs as $sync) {
                $status['recent_syncs'][] = [
                    'type' => $sync['sync_type'],
                    'status' => $sync['status'],
                    'time' => date('Y-m-d H:i:s', $sync['createtime'])
                ];
            }
        } catch (Exception $e) {
            // 获取同步记录失败
        }

        return $status;
    }

    /**
     * 同步门店数据
     * @param array $params 同步参数
     * @return array
     */
    public function syncShops($params = [])
    {
        $startTime = time();
        $this->logSync('shops', 'start', '开始同步门店数据');

        try {
            $mode = $params['mode'] ?? 'database';
            $traderId = $params['trader_id'] ?? null;
            $batchSize = $params['batch_size'] ?? 1000;
            $isDryRun = $params['dry_run'] ?? false;

            if ($mode === 'database') {
                $result = $this->syncShopsFromDatabase($traderId, $batchSize, $isDryRun);
            } else {
                throw new Exception('门店同步暂时只支持数据库模式');
            }

            $result['duration'] = time() - $startTime;
            $this->logSync('shops', 'success', '门店同步完成', $result);

            return $result;

        } catch (Exception $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage(),
                'duration' => time() - $startTime
            ];
            $this->logSync('shops', 'failed', '门店同步失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 从数据库同步门店数据
     * @param string $traderId 商户ID
     * @param int $batchSize 批量大小
     * @param bool $isDryRun 是否试运行
     * @return array
     */
    protected function syncShopsFromDatabase($traderId = null, $batchSize = 1000, $isDryRun = false)
    {
        echo "\n=== 从银枣数据库同步门店数据 ===\n";

        // 构建查询条件
        $where = ['is_del' => 0, 'status' => 1];
        if ($traderId) {
            $where['trader_id'] = $traderId;
        }

        // 获取总数
        $total = DatabaseManager::getTableCount('trader_shop', $where);
        echo "找到 {$total} 个门店需要同步\n";

        if ($total == 0) {
            return [
                'success' => true,
                'total' => 0,
                'success_count' => 0,
                'failed_count' => 0,
                'errors' => []
            ];
        }

        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        $page = 1;

        while (($page - 1) * $batchSize < $total) {
            echo "\n--- 处理第 {$page} 批门店数据 ---\n";

            try {
                // 获取门店数据
                $shops = DatabaseManager::getTableData(
                    'trader_shop',
                    $where,
                    ['*'],
                    $page,
                    $batchSize,
                    'shop_id ASC'
                );

                echo "获取到 " . count($shops) . " 个门店数据\n";

                foreach ($shops as $shop) {
                    try {
                        if (!$isDryRun) {
                            $this->syncSingleShop($shop);
                        }
                        $successCount++;
                        echo "✅ 门店同步成功: {$shop['shop_name']} (ID: {$shop['shop_id']})\n";
                    } catch (Exception $e) {
                        $failedCount++;
                        $error = "门店 {$shop['shop_id']}: " . $e->getMessage();
                        $errors[] = $error;
                        echo "❌ 门店同步失败: {$error}\n";
                    }
                }

                $page++;

            } catch (Exception $e) {
                $error = "批量处理失败: " . $e->getMessage();
                $errors[] = $error;
                echo "❌ {$error}\n";
                break;
            }
        }

        return [
            'success' => $failedCount == 0,
            'total' => $total,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 同步单个门店
     * @param array $shopData 门店数据
     * @return int 本地门店ID
     */
    protected function syncSingleShop($shopData)
    {
        // 开启事务
        Db::startTrans();

        try {
            // 检查门店是否已存在
            $existingShop = Db::name('wanlshop_shop')
                ->where('third_party_id', $shopData['shop_id'])
                ->where('third_party_provider', 'inzayun')
                ->find();

            // 转换数据格式
            $transformedData = $this->transformer->transformShop($shopData);

            // 创建或获取管理用户
            $userId = $this->createOrGetShopUser($shopData);
            if ($userId) {
                $transformedData['user_id'] = $userId;
            }

            if ($existingShop) {
                // 更新现有门店
                Db::name('wanlshop_shop')
                    ->where('id', $existingShop['id'])
                    ->update($transformedData);
                $shopId = $existingShop['id'];
            } else {
                // 创建新门店
                $shopId = Db::name('wanlshop_shop')->insertGetId($transformedData);
            }

            Db::commit();
            return $shopId;

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 创建或获取店铺管理用户
     * @param array $shopData 店铺数据
     * @return int|false 用户ID，失败返回false
     */
    protected function createOrGetShopUser($shopData)
    {
        try {
            // 获取手机号（优先使用shop_mobile，其次shop_tel）
            $mobile = $shopData['shop_mobile'] ?? $shopData['shop_tel'] ?? '';

            if (empty($mobile) || strlen($mobile) < 6) {
                return false; // 手机号无效，无法创建用户
            }

            // 检查是否已存在该手机号的用户
            $existingUser = Db::name('user')
                ->where('mobile', $mobile)
                ->find();

            if ($existingUser) {
                return $existingUser['id'];
            }

            // 创建新用户
            $shopName = $shopData['shop_name'] ?? '';
            $username = $this->generateUsername($mobile, $shopName);
            $password = substr($mobile, -6); // 手机号后六位作为密码
            $salt = \fast\Random::alnum();

            $userData = [
                'username' => $username,
                'nickname' => $shopName ?: $username,
                'password' => \app\common\library\Auth::instance()->getEncryptPassword($password, $salt),
                'salt' => $salt,
                'mobile' => $mobile,
                'email' => '',
                'avatar' => '',
                'level' => 1,
                'gender' => 0,
                'birthday' => null,
                'bio' => '店铺管理员',
                'money' => 0,
                'score' => 0,
                'successions' => 1,
                'maxsuccessions' => 1,
                'prevtime' => time(),
                'logintime' => time(),
                'loginip' => '',
                'loginfailure' => 0,
                'joinip' => '',
                'jointime' => time(),
                'createtime' => time(),
                'updatetime' => time(),
                'token' => '',
                'status' => 'normal'
            ];

            $userId = Db::name('user')->insertGetId($userData);

            if ($userId) {
                echo "✅ 为店铺 {$shopName} 创建管理用户: {$username} (手机: {$mobile}, 密码: {$password})\n";

                // 同时创建店铺认证记录
                $this->createShopAuth($userId, $shopData);
            }

            return $userId;

        } catch (Exception $e) {
            echo "❌ 创建店铺管理用户失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 生成用户名
     * @param string $mobile 手机号
     * @param string $shopName 店铺名称
     * @return string
     */
    protected function generateUsername($mobile, $shopName = '')
    {
        // 基础用户名：shop_ + 手机号后8位
        $baseUsername = 'shop_' . substr($mobile, -8);

        // 检查用户名是否已存在
        $existingUser = Db::name('user')
            ->where('username', $baseUsername)
            ->find();

        if (!$existingUser) {
            return $baseUsername;
        }

        // 如果已存在，添加随机后缀
        $counter = 1;
        do {
            $username = $baseUsername . '_' . $counter;
            $existingUser = Db::name('user')
                ->where('username', $username)
                ->find();
            $counter++;
        } while ($existingUser && $counter < 100);

        return $username;
    }

    /**
     * 创建店铺认证记录
     * @param int $userId 用户ID
     * @param array $shopData 店铺数据
     * @return bool
     */
    protected function createShopAuth($userId, $shopData)
    {
        try {
            // 检查是否已存在认证记录
            $existingAuth = Db::name('wanlshop_auth')
                ->where('user_id', $userId)
                ->find();

            if ($existingAuth) {
                return true; // 已存在，不重复创建
            }

            $shopName = $shopData['shop_name'] ?? '';
            $mobile = $shopData['shop_mobile'] ?? $shopData['shop_tel'] ?? '';

            // 创建认证记录
            $authData = [
                'user_id' => $userId,
                'shopname' => $shopName,
                'state' => '1', // 企业店铺
                'verify' => '3', // 直接设置为审核通过
                'status' => 'normal',
                'mobile' => $mobile,
                'content' => '系统自动同步的店铺',
                'bio' => $shopName . '是通过系统同步创建的店铺',
                'city' => $shopData['shop_address'] ?? '',
                'avatar' => '',
                'createtime' => time(),
                'updatetime' => time()
            ];

            $authId = Db::name('wanlshop_auth')->insertGetId($authData);

            if ($authId) {
                echo "✅ 为用户 {$userId} 创建店铺认证记录: {$shopName} (认证ID: {$authId})\n";
                return true;
            }

            return false;

        } catch (Exception $e) {
            echo "❌ 创建店铺认证记录失败: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 同步分类数据
     * @param array $params 同步参数
     * @return array
     */
    public function syncCategories($params = [])
    {
        $startTime = time();
        $this->logSync('categories', 'start', '开始同步分类数据');

        try {
            $mode = $params['mode'] ?? 'database';
            $traderId = $params['trader_id'] ?? null;
            $batchSize = $params['batch_size'] ?? 1000;
            $isDryRun = $params['dry_run'] ?? false;

            if ($mode === 'database') {
                $result = $this->syncCategoriesFromDatabase($traderId, $batchSize, $isDryRun);
            } else {
                throw new Exception('分类同步暂时只支持数据库模式');
            }

            $result['duration'] = time() - $startTime;
            $this->logSync('categories', 'success', '分类同步完成', $result);

            return $result;

        } catch (Exception $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage(),
                'duration' => time() - $startTime
            ];
            $this->logSync('categories', 'failed', '分类同步失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 从数据库同步分类数据
     * @param string $traderId 商户ID
     * @param int $batchSize 批量大小
     * @param bool $isDryRun 是否试运行
     * @return array
     */
    protected function syncCategoriesFromDatabase($traderId = null, $batchSize = 1000, $isDryRun = false)
    {
        echo "\n=== 从银枣数据库同步分类数据 ===\n";

        // 构建查询条件
        $where = ['is_del' => 0, 'status' => 1];
        if ($traderId) {
            $where['trader_id'] = $traderId;
        }

        // 获取总数
        $total = DatabaseManager::getTableCount('goods_type', $where);
        echo "找到 {$total} 个分类需要同步\n";

        if ($total == 0) {
            return [
                'success' => true,
                'total' => 0,
                'success_count' => 0,
                'failed_count' => 0,
                'errors' => []
            ];
        }

        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        $page = 1;

        while (($page - 1) * $batchSize < $total) {
            echo "\n--- 处理第 {$page} 批分类数据 ---\n";

            try {
                // 获取分类数据
                $categories = DatabaseManager::getTableData(
                    'goods_type',
                    $where,
                    ['*'],
                    $page,
                    $batchSize,
                    'type_sort ASC, type_id ASC'
                );

                echo "获取到 " . count($categories) . " 个分类数据\n";

                foreach ($categories as $category) {
                    try {
                        if (!$isDryRun) {
                            $this->syncSingleCategory($category);
                        }
                        $successCount++;
                        echo "✅ 分类同步成功: {$category['type_name']} (ID: {$category['type_id']})\n";
                    } catch (Exception $e) {
                        $failedCount++;
                        $error = "分类 {$category['type_id']}: " . $e->getMessage();
                        $errors[] = $error;
                        echo "❌ 分类同步失败: {$error}\n";
                    }
                }

                $page++;

            } catch (Exception $e) {
                $error = "批量处理失败: " . $e->getMessage();
                $errors[] = $error;
                echo "❌ {$error}\n";
                break;
            }
        }

        return [
            'success' => $failedCount == 0,
            'total' => $total,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 同步单个分类
     * @param array $categoryData 分类数据
     * @return int 本地分类ID
     */
    protected function syncSingleCategory($categoryData)
    {
        // 开启事务
        Db::startTrans();

        try {
            // 检查分类是否已存在
            $existingCategory = Db::name('wanlshop_category')
                ->where('third_party_id', $categoryData['type_id'])
                ->where('third_party_provider', 'inzayun')
                ->find();

            // 转换数据格式
            $transformedData = $this->transformer->transformCategory($categoryData);

            if ($existingCategory) {
                // 更新现有分类
                Db::name('wanlshop_category')
                    ->where('id', $existingCategory['id'])
                    ->update($transformedData);
                $categoryId = $existingCategory['id'];
            } else {
                // 创建新分类
                $categoryId = Db::name('wanlshop_category')->insertGetId($transformedData);
            }

            Db::commit();
            return $categoryId;

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 同步单个品牌
     * @param array $brandData 品牌数据
     * @throws Exception
     */
    protected function syncSingleBrand($brandData)
    {
        Db::startTrans();
        try {
            // 使用DatabaseTransformer转换品牌数据
            $transformer = new DatabaseTransformer();
            $localBrandData = $transformer->transformBrand($brandData);

            // 检查品牌是否已存在
            $existingBrand = Db::name('wanlshop_brand')
                ->where('third_party_provider', 'inzayun')
                ->where('third_party_id', $brandData['brand_id'])
                ->find();

            if ($existingBrand) {
                // 更新现有品牌
                $localBrandData['updatetime'] = time();
                Db::name('wanlshop_brand')
                    ->where('id', $existingBrand['id'])
                    ->update($localBrandData);
            } else {
                // 创建新品牌
                $localBrandData['createtime'] = time();
                $localBrandData['updatetime'] = time();
                Db::name('wanlshop_brand')->insert($localBrandData);
            }

            Db::commit();

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 同步品牌数据
     * @param array $params 同步参数
     * @return array
     */
    public function syncBrands($params = [])
    {
        $startTime = time();
        $this->logSync('brands', 'start', '开始同步品牌数据');

        try {
            $mode = $params['mode'] ?? 'database';
            $traderId = $params['trader_id'] ?? null;
            $batchSize = $params['batch_size'] ?? 1000;
            $isDryRun = $params['dry_run'] ?? false;

            if ($mode === 'database') {
                $result = $this->syncBrandsFromDatabase($traderId, $batchSize, $isDryRun);
            } else {
                throw new Exception('品牌同步暂时只支持数据库模式');
            }

            $result['duration'] = time() - $startTime;
            $this->logSync('brands', 'success', '品牌同步完成', $result);

            return $result;

        } catch (Exception $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage(),
                'duration' => time() - $startTime
            ];
            $this->logSync('brands', 'failed', '品牌同步失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 同步商品数据
     * @param array $params 同步参数
     * @return array
     */
    public function syncGoods($params = [])
    {
        $startTime = time();
        $this->logSync('goods', 'start', '开始同步商品数据');

        try {
            $mode = $params['mode'] ?? 'database';
            $traderId = $params['trader_id'] ?? null;
            $shopId = $params['shop_id'] ?? null;
            $batchSize = $params['batch_size'] ?? 1000;
            $isDryRun = $params['dry_run'] ?? false;

            if ($mode === 'database') {
                $result = $this->syncGoodsFromDatabase($traderId, $shopId, $batchSize, $isDryRun);
            } else {
                throw new Exception('商品同步暂时只支持数据库模式');
            }

            $result['duration'] = time() - $startTime;
            $this->logSync('goods', 'success', '商品同步完成', $result);

            return $result;

        } catch (Exception $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage(),
                'duration' => time() - $startTime
            ];
            $this->logSync('goods', 'failed', '商品同步失败: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 从数据库同步品牌数据
     * @param string $traderId 商户ID
     * @param int $batchSize 批量大小
     * @param bool $isDryRun 是否试运行
     * @return array
     */
    protected function syncBrandsFromDatabase($traderId = null, $batchSize = 1000, $isDryRun = false)
    {
        echo "\n=== 从银枣数据库同步品牌数据 ===\n";

        // 构建查询条件
        $where = ['is_del' => 0, 'status' => 1];
        if ($traderId) {
            $where['trader_id'] = $traderId;
        }

        // 获取总数
        $total = DatabaseManager::getTableCount('goods_brand', $where);
        echo "找到 {$total} 个品牌需要同步\n";

        if ($total == 0) {
            return [
                'success' => true,
                'total' => 0,
                'success_count' => 0,
                'failed_count' => 0,
                'errors' => []
            ];
        }

        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        $page = 1;

        while (($page - 1) * $batchSize < $total) {
            echo "\n--- 处理第 {$page} 批品牌数据 ---\n";

            try {
                // 获取品牌数据
                $brands = DatabaseManager::getTableData(
                    'goods_brand',
                    $where,
                    ['*'],
                    $page,
                    $batchSize,
                    'brand_id ASC'
                );

                echo "获取到 " . count($brands) . " 个品牌数据\n";

                foreach ($brands as $brand) {
                    try {
                        if (!$isDryRun) {
                            $this->syncSingleBrand($brand);
                        }
                        $successCount++;
                        echo "✅ 品牌同步成功: {$brand['brand_name']} (ID: {$brand['brand_id']})\n";
                    } catch (Exception $e) {
                        $failedCount++;
                        $error = "品牌 {$brand['brand_id']}: " . $e->getMessage();
                        $errors[] = $error;
                        echo "❌ 品牌同步失败: {$error}\n";
                    }
                }

                $page++;

            } catch (Exception $e) {
                $error = "批量处理失败: " . $e->getMessage();
                $errors[] = $error;
                echo "❌ {$error}\n";
                break;
            }
        }

        return [
            'success' => $failedCount == 0,
            'total' => $total,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 从数据库同步商品数据（使用goods_main主表关联电商商品信息）
     * @param string $traderId 商户ID
     * @param string $shopId 门店ID
     * @param int $batchSize 批量大小
     * @param bool $isDryRun 是否试运行
     * @return array
     */
    protected function syncGoodsFromDatabase($traderId = null, $shopId = null, $batchSize = 1000, $isDryRun = false)
    {
        echo "\n=== 从银枣数据库同步商品数据（关联电商信息） ===\n";

        // 构建查询条件 - 使用goods_main作为主表
        // 修改：移除 status = 1 限制，同步所有商品（包括下架商品）以更新状态
        $where = ['gm.is_del' => 0];
        if ($traderId) {
            $where['gm.trader_id'] = $traderId;
        }

        // 获取总数 - 从商品主表关联电商商品表
        $countSql = "
            SELECT COUNT(DISTINCT gm.goods_id) as total
            FROM goods_main gm
            LEFT JOIN commerce_goods_detail cgd ON gm.goods_id = cgd.goods_id AND cgd.is_del = 0
            WHERE cgd.commerce_goods_detail_id IS NOT NULL
        ";
        if ($traderId) {
            // 针对特定trader_id使用不同的查询条件
            if ($traderId == 27500) {
                $countSql .= " AND gm.trader_id = {$traderId}";
            } else {
                // 修改：移除 status = 1 限制，同步所有商品状态
                $countSql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0";
            }
        } else {
            // 全量同步时，需要包含特殊trader_id的商品
            // 修改：移除 status = 1 限制，同步所有商品状态
            $countSql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0))";
        }

        $countResult = DatabaseManager::query($countSql);
        $total = $countResult[0]['total'] ?? 0;
        echo "找到 {$total} 个商品需要同步\n";

        if ($total == 0) {
            return [
                'success' => true,
                'total' => 0,
                'success_count' => 0,
                'failed_count' => 0,
                'errors' => []
            ];
        }

        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        $page = 1;

        while (($page - 1) * $batchSize < $total) {
            echo "\n--- 处理第 {$page} 批商品数据 ---\n";

            try {
                // 获取商品数据（goods_main为主表，关联电商商品信息）
                // 基于实际数据库表结构的查询
                $sql = "
                    SELECT
                        gm.*,
                        gt.type_name,
                        gb.barcode_code,
                        -- 品牌信息
                        gbr.brand_name,
                        gbr.brand_code,
                        -- 库存信息（按门店分组）
                        im.usable_xnum_sum,
                        im.xnum_sum,
                        im.lock_xnum_sum,
                        im.lock_xnum_tmp,
                        im.avg_cost_price,
                        im.shop_id as inventory_shop_id,
                        -- 门店价格信息
                        gsp.price as goods_shop_price,
                        gsp.member_price as goods_shop_member_price,
                        cgsp.price as commerce_shop_price,
                        cgsp.member_price as commerce_shop_member_price,
                        -- 商品图片信息
                        GROUP_CONCAT(DISTINCT gp.picture_url) as picture_urls,
                        -- 关联电商商品详情信息
                        cgd.sell_price as commerce_price,
                        cgd.stock as commerce_stock,
                        cgd.weight as commerce_weight,
                        cgd.sort as commerce_sort,
                        cgd.status as commerce_status,
                        cgd.commerce_shop_industry_type_id,
                        cgd.shop_id as commerce_shop_id,
                        -- 电商商品图片
                        GROUP_CONCAT(DISTINCT cgdi.thumb) as commerce_image_urls,
                        -- 电商商品标签
                        GROUP_CONCAT(DISTINCT cgnl_detail.title) as commerce_notes_labels,
                        -- 电商商品详情（使用MAX避免GROUP BY问题）
                        MAX(cgdd.details) as commerce_goods_details
                    FROM goods_main gm
                    LEFT JOIN goods_type gt ON gm.type_id = gt.type_id
                    LEFT JOIN goods_barcode gb ON gm.goods_id = gb.goods_id
                    LEFT JOIN goods_brand gbr ON gm.brand_id = gbr.brand_id AND gbr.trader_id = gm.trader_id
                    LEFT JOIN inventory_main im ON gm.goods_id = im.goods_id AND im.trader_id = gm.trader_id
                    LEFT JOIN goods_picture gp ON gm.goods_id = gp.goods_id
                    -- 关联门店价格表
                    LEFT JOIN goods_shop_price gsp ON gm.goods_id = gsp.goods_id AND gsp.trader_id = gm.trader_id
                    LEFT JOIN commerce_goods_shop_price cgsp ON gm.goods_id = cgsp.goods_id AND cgsp.trader_id = gm.trader_id
                    -- 关联电商商品表（使用实际存在的表和字段）
                    LEFT JOIN commerce_goods_detail cgd ON gm.goods_id = cgd.goods_id AND cgd.is_del = 0
                    LEFT JOIN commerce_goods_detail_img cgdi ON cgd.commerce_goods_detail_id = cgdi.commerce_goods_detail_id
                    LEFT JOIN commerce_goods_detail_notes_label cgnl ON cgd.commerce_goods_detail_id = cgnl.commerce_goods_detail_id
                    LEFT JOIN commerce_goods_notes_label cgnl_detail ON cgnl.commerce_goods_notes_label_id = cgnl_detail.commerce_goods_notes_label_id AND cgnl_detail.is_del = 0
                    -- 关联电商商品详情表
                    LEFT JOIN commerce_goods_detail_details cgdd ON cgd.commerce_goods_detail_id = cgdd.commerce_goods_detail_id
                    WHERE 1=1
                ";

                if ($traderId) {
                    // 针对特定trader_id使用不同的查询条件
                    if ($traderId == 27500) {
                        $sql .= " AND gm.trader_id = {$traderId}";
                    } else {
                        // 修改：移除 status = 1 限制，同步所有商品状态
                        $sql .= " AND gm.trader_id = {$traderId} AND gm.is_del = 0";
                    }
                } else {
                    // 全量同步时，需要包含特殊trader_id的商品
                    // 修改：移除 status = 1 限制，同步所有商品状态
                    $sql .= " AND ((gm.trader_id = 27500) OR (gm.is_del = 0))";
                }

                if ($shopId) {
                    $sql .= " AND (cgd.shop_id = {$shopId} OR im.shop_id = {$shopId})";
                }

                // 确保只同步有电商信息的商品
                $sql .= " AND cgd.commerce_goods_detail_id IS NOT NULL";

                $sql .= " GROUP BY gm.goods_id";
                $sql .= " ORDER BY gm.goods_id ASC";
                $sql .= " LIMIT " . (($page - 1) * $batchSize) . ", {$batchSize}";

                // 获取商品数据
                $goods = DatabaseManager::query($sql);

                // 处理混合商品数据（goods_main + 电商商品信息）
                foreach ($goods as &$goodsItem) {
                    // 处理图片URLs - 优先使用goods_main.thumb，过滤无效图片
                    $imageUrls = [];

                    // 优先使用商品主表的thumb
                    if (!empty($goodsItem['thumb']) && !$this->isDefaultImage($goodsItem['thumb'])) {
                        $imageUrls[] = $goodsItem['thumb'];
                    }

                    // 然后使用goods_picture表的图片
                    if (!empty($goodsItem['picture_urls'])) {
                        $pictureUrls = explode(',', $goodsItem['picture_urls']);
                        foreach ($pictureUrls as $url) {
                            if (!empty($url) && !$this->isDefaultImage($url)) {
                                $imageUrls[] = $url;
                            }
                        }
                    }

                    // 最后使用电商图片（如果不是默认图片）
                    if (!empty($goodsItem['commerce_image_urls'])) {
                        $commerceUrls = explode(',', $goodsItem['commerce_image_urls']);
                        foreach ($commerceUrls as $url) {
                            if (!empty($url) && !$this->isDefaultImage($url)) {
                                $imageUrls[] = $url;
                            }
                        }
                    }

                    $goodsItem['picture_urls'] = implode(',', array_unique(array_filter($imageUrls)));

                    // 处理规格标签
                    if (!empty($goodsItem['commerce_notes_labels'])) {
                        $goodsItem['notesLabels'] = explode(',', $goodsItem['commerce_notes_labels']);
                    } else {
                        $goodsItem['notesLabels'] = [];
                    }

                    // 统一字段名称 - 保持goods_main的字段为主
                    $goodsItem['goods_name'] = $goodsItem['goods_name'] ?? '';

                    // 价格信息 - 优先级：门店价格 > 电商价格 > 商品主表价格
                    $commercePrice = floatval($goodsItem['commerce_price'] ?? 0);
                    $goodsPrice = floatval($goodsItem['goods_price'] ?? 0);
                    $goodsShopPrice = floatval($goodsItem['goods_shop_price'] ?? 0);
                    $commerceShopPrice = floatval($goodsItem['commerce_shop_price'] ?? 0);

                    // 价格优先级处理
                    if ($commerceShopPrice > 0) {
                        $finalPrice = $commerceShopPrice;
                    } elseif ($goodsShopPrice > 0) {
                        $finalPrice = $goodsShopPrice;
                    } elseif ($commercePrice > 0 && $commercePrice != -1.00) {
                        $finalPrice = $commercePrice;
                    } else {
                        $finalPrice = $goodsPrice;
                    }

                    // 会员价处理
                    $goodsShopMemberPrice = floatval($goodsItem['goods_shop_member_price'] ?? 0);
                    $commerceShopMemberPrice = floatval($goodsItem['commerce_shop_member_price'] ?? 0);
                    $goodsMemberPrice = floatval($goodsItem['goods_member_price'] ?? 0);

                    if ($commerceShopMemberPrice > 0) {
                        $finalMemberPrice = $commerceShopMemberPrice;
                    } elseif ($goodsShopMemberPrice > 0) {
                        $finalMemberPrice = $goodsShopMemberPrice;
                    } else {
                        $finalMemberPrice = $goodsMemberPrice;
                    }

                    $goodsItem['final_price'] = $this->validatePrice($finalPrice);
                    $goodsItem['final_member_price'] = $this->validatePrice($finalMemberPrice);

                    // 库存信息 - 优先使用inventory_main库存，其次使用电商库存
                    $inventoryStock = intval($goodsItem['usable_xnum_sum'] ?? 0);
                    $commerceStock = intval($goodsItem['commerce_stock'] ?? 0);

                    // 如果有库存表数据，优先使用；否则使用电商库存
                    if ($inventoryStock > 0) {
                        $goodsItem['final_stock'] = $inventoryStock;
                    } elseif ($commerceStock > 0 && $commerceStock != 9999) {
                        // 9999通常表示无限库存，转换为实际库存数量
                        $goodsItem['final_stock'] = $commerceStock;
                    } else {
                        // 对于没有库存数据的商品，设置合理的默认库存
                        $goodsItem['final_stock'] = 100; // 设置默认库存为100
                    }

                    // 其他信息 - 使用可用的数据
                    $goodsItem['final_weight'] = $goodsItem['commerce_weight'] ?: '0';
                    $goodsItem['final_unit'] = $goodsItem['unit_name'] ?? '';
                    $goodsItem['final_sort'] = $goodsItem['commerce_sort'] ?: $goodsItem['sort'] ?? 0;

                    // 分类信息 - 优先使用电商分类
                    $goodsItem['final_type_id'] = $goodsItem['commerce_shop_industry_type_id'] ?: $goodsItem['type_id'] ?? 0;

                    // 门店信息 - 优先使用电商门店，其次使用库存门店
                    $goodsItem['final_shop_id'] = $goodsItem['commerce_shop_id'] ?: $goodsItem['inventory_shop_id'] ?? null;

                    // 保持原有字段以兼容转换器
                    $goodsItem['shop_price'] = $goodsItem['final_price'];
                    $goodsItem['shop_member_price'] = $goodsItem['final_member_price'];
                    $goodsItem['usable_xnum_sum'] = $goodsItem['final_stock'];
                    $goodsItem['goods_weight'] = $goodsItem['final_weight'];
                    $goodsItem['unit_name'] = $goodsItem['final_unit'];
                    $goodsItem['sort'] = $goodsItem['final_sort'];
                    $goodsItem['shop_id'] = $goodsItem['final_shop_id'];

                    // 确保库存数据为数字
                    if (!isset($goodsItem['usable_xnum_sum']) || $goodsItem['usable_xnum_sum'] === null) {
                        $goodsItem['usable_xnum_sum'] = 0;
                    }
                    if (!isset($goodsItem['xnum_sum']) || $goodsItem['xnum_sum'] === null) {
                        $goodsItem['xnum_sum'] = 0;
                    }
                    if (!isset($goodsItem['lock_xnum_sum']) || $goodsItem['lock_xnum_sum'] === null) {
                        $goodsItem['lock_xnum_sum'] = 0;
                    }
                    if (!isset($goodsItem['lock_xnum_tmp']) || $goodsItem['lock_xnum_tmp'] === null) {
                        $goodsItem['lock_xnum_tmp'] = 0;
                    }
                    if (!isset($goodsItem['avg_cost_price']) || $goodsItem['avg_cost_price'] === null) {
                        $goodsItem['avg_cost_price'] = 0;
                    }
                }

                echo "获取到 " . count($goods) . " 个商品数据\n";

                foreach ($goods as $goodsItem) {
                    try {
                        if (!$isDryRun) {
                            $localGoodsId = $this->syncSingleGoods($goodsItem);

                            // 同步商品的额外信息
                            $this->syncGoodsExtendedInfo($goodsItem, $localGoodsId);
                        }
                        $successCount++;

                        // 显示详细的商品信息
                        $goodsInfo = $this->formatGoodsInfo($goodsItem);
                        echo "✅ 商品同步成功: {$goodsInfo}\n";

                    } catch (Exception $e) {
                        $failedCount++;
                        $error = "商品 {$goodsItem['goods_id']}: " . $e->getMessage();
                        $errors[] = $error;
                        echo "❌ 商品同步失败: {$error}\n";
                    }
                }

                $page++;

            } catch (Exception $e) {
                $error = "批量处理失败: " . $e->getMessage();
                $errors[] = $error;
                echo "❌ {$error}\n";
                break;
            }
        }

        return [
            'success' => $failedCount == 0,
            'total' => $total,
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'errors' => $errors
        ];
    }

    /**
     * 同步单个商品
     * @param array $goodsData 商品数据
     * @return int 本地商品ID
     */
    protected function syncSingleGoods($goodsData)
    {
        // 开启事务
        Db::startTrans();

        try {
            // 检查商品是否已存在
            $existingGoods = Db::name('wanlshop_goods')
                ->where('third_party_id', $goodsData['goods_id'])
                ->where('third_party_provider', 'inzayun')
                ->find();

            // 转换数据格式
            $transformedData = $this->transformer->transformGoods($goodsData);

            if ($existingGoods) {
                // 更新现有商品
                Db::name('wanlshop_goods')
                    ->where('id', $existingGoods['id'])
                    ->update($transformedData);
                $goodsId = $existingGoods['id'];
            } else {
                // 创建新商品
                $goodsId = Db::name('wanlshop_goods')->insertGetId($transformedData);
            }

            Db::commit();
            return $goodsId;

        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 同步商品扩展信息
     * @param array $goodsData 商品数据
     * @param int $localGoodsId 本地商品ID
     */
    protected function syncGoodsExtendedInfo($goodsData, $localGoodsId)
    {
        $skuSynced = false;
        $spuSynced = false;

        try {
            // 1. 同步商品SKU
            try {
                $this->syncGoodsSku($goodsData, $localGoodsId);
                $skuSynced = true;
            } catch (Exception $e) {
                echo "    ⚠️ SKU同步失败: " . $e->getMessage() . "\n";
                // SKU同步失败不应该阻止SPU同步
            }

            // 2. 同步商品SPU
            try {
                $this->syncGoodsSpu($goodsData, $localGoodsId);
                $spuSynced = true;
            } catch (Exception $e) {
                echo "    ⚠️ SPU同步失败: " . $e->getMessage() . "\n";
                // SPU同步失败不应该阻止整个流程
            }

            // 3. 记录详细的商品信息到日志
            $extendedInfo = [
                'local_goods_id' => $localGoodsId,
                'third_party_goods_id' => $goodsData['goods_id'],
                'trader_id' => $goodsData['trader_id'],
                'type_id' => $goodsData['type_id'],
                'brand_name' => $goodsData['brand_name'] ?? '',
                'goods_size' => $goodsData['goods_size'] ?? '',
                'supplier_id' => $goodsData['supplier_id'] ?? '',
                'picture_count' => !empty($goodsData['picture_urls']) ? count(explode(',', $goodsData['picture_urls'])) : 0,
                'has_shop_price' => !empty($goodsData['shop_price']),
                'sku_synced' => $skuSynced,
                'spu_synced' => $spuSynced,
            ];

            if ($skuSynced && $spuSynced) {
                echo "    ✅ 扩展信息同步完成\n";
            } else {
                echo "    ⚠️ 扩展信息部分同步完成 (SKU: " . ($skuSynced ? '✅' : '❌') . ", SPU: " . ($spuSynced ? '✅' : '❌') . ")\n";
            }

        } catch (Exception $e) {
            echo "    ⚠️ 扩展信息同步失败: " . $e->getMessage() . "\n";
            // 扩展信息同步失败不应该影响主要的商品同步流程
            // 所以这里只记录错误，不抛出异常
        }
    }

    /**
     * 同步商品SKU
     * @param array $goodsData 商品数据
     * @param int $localGoodsId 本地商品ID
     */
    protected function syncGoodsSku($goodsData, $localGoodsId)
    {
        // 银枣系统中，商品可能没有独立的SKU表，而是在商品主表中包含SKU信息
        // 我们创建一个默认的SKU记录

        $thirdPartySkuId = $goodsData['goods_id'] ?? '';

        // 检查是否已存在SKU - 使用更精确的查询条件
        $existingSku = Db::name('wanlshop_goods_sku')
            ->where('goods_id', $localGoodsId)
            ->where(function($query) use ($thirdPartySkuId) {
                $query->where('third_party_sku_id', $thirdPartySkuId)
                      ->whereOr('third_party_sku_id', 'null')
                      ->whereOr('third_party_sku_id', '');
            })
            ->find();

        // 构建SKU数据 - 包含完整的库存信息和商品名称
        $skuData = [
            'sku_id' => $thirdPartySkuId,
            'goods_id' => $goodsData['goods_id'], // 添加goods_id用于转换器
            'goods_name' => $goodsData['goods_name'] ?? '', // 添加商品名称用于规格
            'sku_price' => $this->validatePrice($goodsData['goods_price'] ?? 0),
            'sku_market_price' => $this->validatePrice($goodsData['goods_purchase_price'] ?? $goodsData['goods_price'] ?? 0),

            // 传递所有库存相关字段给转换器
            'usable_xnum_sum' => $goodsData['usable_xnum_sum'] ?? null,
            'xnum_sum' => $goodsData['xnum_sum'] ?? null,
            'lock_xnum_sum' => $goodsData['lock_xnum_sum'] ?? null,
            'lock_xnum_tmp' => $goodsData['lock_xnum_tmp'] ?? null,
            'stock_high' => $goodsData['stock_high'] ?? null,
            'stock_low' => $goodsData['stock_low'] ?? null,
            'goods_stock' => $this->extractInzayunStock($goodsData), // 备用字段

            'sku_weight' => $goodsData['goods_weight'] ?? '0',
            'sku_code' => $goodsData['goods_code'] ?? '',
            'sku_sales' => $goodsData['goods_sales'] ?? 0,
            'sku_image' => $goodsData['thumb'] ?? '',
            'spec_info' => $this->buildDefaultSpecInfo($goodsData),

            // 传递完整的商品数据给转换器
            'final_price' => $this->validatePrice($goodsData['final_price'] ?? $goodsData['shop_price'] ?? 0),
            'final_weight' => $goodsData['final_weight'] ?? $goodsData['goods_weight'] ?? '0',
            'final_sales' => $goodsData['final_sales'] ?? $goodsData['goods_sales'] ?? 0,
            'thumb' => $goodsData['thumb'] ?? '',
            'barcode_code' => $goodsData['barcode_code'] ?? '',
        ];

        // 转换SKU数据
        $transformedSku = $this->transformer->transformSku($skuData, $localGoodsId);

        try {
            if ($existingSku) {
                // 更新现有SKU
                Db::name('wanlshop_goods_sku')
                    ->where('id', $existingSku['id'])
                    ->update($transformedSku);
                echo "    ✅ SKU更新成功 (ID: {$existingSku['id']})\n";
            } else {
                // 创建新SKU - 使用事务确保数据一致性
                Db::startTrans();
                try {
                    $skuId = Db::name('wanlshop_goods_sku')->insertGetId($transformedSku);
                    Db::commit();
                    echo "    ✅ SKU创建成功 (ID: {$skuId})\n";
                } catch (Exception $e) {
                    Db::rollback();
                    // 如果是重复键错误，尝试更新现有记录
                    if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                        echo "    ⚠️ SKU重复，尝试更新现有记录\n";
                        $existingSku = Db::name('wanlshop_goods_sku')
                            ->where('third_party_sku_id', $thirdPartySkuId)
                            ->where('goods_id', $localGoodsId)
                            ->find();
                        if ($existingSku) {
                            Db::name('wanlshop_goods_sku')
                                ->where('id', $existingSku['id'])
                                ->update($transformedSku);
                            echo "    ✅ SKU更新成功 (ID: {$existingSku['id']})\n";
                        } else {
                            throw $e;
                        }
                    } else {
                        throw $e;
                    }
                }
            }
        } catch (Exception $e) {
            echo "    ❌ SKU同步失败: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    /**
     * 同步商品SPU
     * @param array $goodsData 商品数据
     * @param int $localGoodsId 本地商品ID
     */
    protected function syncGoodsSpu($goodsData, $localGoodsId)
    {
        // 银枣系统中可能没有独立的SPU概念，我们根据商品信息创建默认的SPU

        // 检查是否已存在SPU
        $existingSpu = Db::name('wanlshop_goods_spu')
            ->where('goods_id', $localGoodsId)
            ->find();

        // 构建SPU数据
        $spuData = [
            'goods_id' => $goodsData['goods_id'] ?? '',
            'goods_name' => $goodsData['goods_name'] ?? '',
            'spec_name' => $this->extractSpecName($goodsData),
            'spec_items' => $this->extractSpecItems($goodsData),
        ];

        // 转换SPU数据
        $transformedSpu = $this->transformer->transformSpu($spuData, $localGoodsId);

        if ($existingSpu) {
            // 更新现有SPU
            Db::name('wanlshop_goods_spu')
                ->where('id', $existingSpu['id'])
                ->update($transformedSpu);
            echo "    ✅ SPU更新成功 (ID: {$existingSpu['id']})\n";
        } else {
            // 创建新SPU
            $spuId = Db::name('wanlshop_goods_spu')->insertGetId($transformedSpu);
            echo "    ✅ SPU创建成功 (ID: {$spuId})\n";
        }
    }

    /**
     * 格式化商品信息用于显示
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function formatGoodsInfo($goodsData)
    {
        $info = [];

        // 商品名称和ID
        $info[] = $goodsData['goods_name'] . " (ID: {$goodsData['goods_id']})";

        // 商户信息
        if (!empty($goodsData['trader_id'])) {
            $info[] = "商户: {$goodsData['trader_id']}";
        }

        // 分类信息
        if (!empty($goodsData['type_name'])) {
            $info[] = "分类: {$goodsData['type_name']}";
        }

        // 价格信息
        if (!empty($goodsData['goods_price'])) {
            $info[] = "价格: ¥{$goodsData['goods_price']}";
        }

        // 品牌信息
        if (!empty($goodsData['brand_name'])) {
            $info[] = "品牌: {$goodsData['brand_name']}";
        }

        // 规格信息
        if (!empty($goodsData['goods_size'])) {
            $info[] = "规格: {$goodsData['goods_size']}";
        }

        // 单位信息
        if (!empty($goodsData['unit_name'])) {
            $info[] = "单位: {$goodsData['unit_name']}";
        }

        return implode(' | ', $info);
    }

    /**
     * 记录同步日志
     * @param string $type 同步类型
     * @param string $status 状态
     * @param string $message 消息
     * @param array $data 数据
     */
    protected function logSync($type, $status, $message, $data = [])
    {
        $logEntry = [
            'type' => $type,
            'status' => $status,
            'message' => $message,
            'data' => $data,
            'time' => date('Y-m-d H:i:s')
        ];

        $this->syncLog[] = $logEntry;

        // 保存到数据库
        try {
            Db::name('sync_log')->insert([
                'sync_type' => $type,
                'sync_provider' => 'inzayun',
                'status' => $status,
                'message' => $message,
                'sync_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'createtime' => time()
            ]);
        } catch (Exception $e) {
            // 日志记录失败，不影响主流程
        }
    }

    /**
     * 构建默认规格信息（单规格商品）
     * @param array $goodsData 商品数据
     * @return array
     */
    protected function buildDefaultSpecInfo($goodsData)
    {
        // 银枣系统是单规格的，使用商品名称作为规格值
        $specName = '规格';
        $specValue = $goodsData['goods_name'] ?? '标准规格';

        return [
            ['name' => $specName, 'value' => $specValue]
        ];
    }

    /**
     * 提取规格名称（单规格商品）
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function extractSpecName($goodsData)
    {
        // 银枣系统是单规格的，统一使用"规格"作为规格名称
        return '规格';
    }

    /**
     * 提取规格项目（单规格商品）
     * @param array $goodsData 商品数据
     * @return array
     */
    protected function extractSpecItems($goodsData)
    {
        // 银枣系统是单规格的，使用商品名称作为规格项目
        $specValue = $goodsData['goods_name'] ?? '标准规格';

        return [$specValue];
    }

    /**
     * 从银枣数据中提取库存
     * @param array $goodsData 商品数据
     * @return int
     */
    protected function extractInzayunStock($goodsData)
    {
        // 银枣数据库中的库存字段优先级顺序
        $stockFields = [
            // 来自inventory_main表的字段（最准确）
            'usable_xnum_sum',  // 实际可出库数量
            'xnum_sum',         // 库存总量

            // 来自goods_main表的字段
            'stock_high',       // 库存高位
            'stock_low',        // 库存低位

            // 其他可能的库存字段
            'goods_stock',      // 商品库存
            'stock',            // 库存
            'inventory',        // 库存
            'qty',              // 数量
            'quantity',         // 数量
            'num',              // 数量
            'goods_num',        // 商品数量
            'available_stock',  // 可用库存
            'current_stock',    // 当前库存
            'real_stock'        // 实际库存
        ];

        foreach ($stockFields as $field) {
            if (isset($goodsData[$field]) && is_numeric($goodsData[$field])) {
                $stock = floatval($goodsData[$field]);
                // 确保库存不为负数，并转换为整数
                $finalStock = max(0, intval($stock));
                echo "    📦 库存字段 {$field}: {$stock} → {$finalStock}\n";
                return $finalStock;
            }
        }

        // 如果没有找到库存字段，返回默认值
        echo "    ⚠️ 未找到库存字段，使用默认值999\n";
        return 999;
    }

    /**
     * 获取同步日志
     * @return array
     */
    public function getSyncLog()
    {
        return $this->syncLog;
    }

    /**
     * 验证价格范围
     * @param mixed $price 价格
     * @return float 验证后的价格
     */
    protected function validatePrice($price)
    {
        $price = floatval($price);

        // 价格范围检查 - decimal(10,2) unsigned 的范围是 0.00 到 99999999.99
        if ($price < 0) {
            echo "    ⚠️ 价格为负数，已修正为0: {$price} → 0.00\n";
            return 0.00;
        } elseif ($price > 99999999.99) {
            echo "    ⚠️ 价格超出范围，已修正: {$price} → 99999999.99\n";
            return 99999999.99;
        }

        return round($price, 2);
    }

    /**
     * 检查是否为默认图片
     * @param string $imageUrl 图片URL
     * @return bool
     */
    protected function isDefaultImage($imageUrl)
    {
        if (empty($imageUrl)) {
            return true;
        }

        // 检查是否为默认图片URL
        $defaultPatterns = [
            '/default\.png$/',
            '/default\.jpg$/',
            '/default\.jpeg$/',
            '/placeholder/',
            '/no-image/',
            '/404/'
        ];

        foreach ($defaultPatterns as $pattern) {
            if (preg_match($pattern, $imageUrl)) {
                return true;
            }
        }

        return false;
    }
}
