<?php

namespace app\common\model;

use fast\Random;
use think\Db;
use think\Exception;
use think\Model;

/**
 * 会员模型
 */
class User extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];

    /**
     * 获取个人URL
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getUrlAttr($value, $data)
    {
        return "/u/" . $data['id'];
    }

    /**
     * 获取头像
     * @param string $value
     * @param array  $data
     * @return string
     */
    public function getAvatarAttr($value, $data)
    {
        if (!$value) {
            //如果不需要启用首字母头像，请使用
            //$value = '/assets/img/avatar.png';
            $value = letter_avatar($data['nickname']);
        }
        return $value;
    }

    /**
     * 获取会员的组别
     */
    public function getGroupAttr($value, $data)
    {
        return UserGroup::get($data['group_id']);
    }

    /**
     * 生成邀请码
     * @param int $length
     * @return string
     */
    public static function generateInviteCode($length = 8)
    {
        $code = Random::alnum($length);
        $result = self::where('invite_code', $code)->count();
        if ($result > 0) {
            return self::generateInviteCode();
        }
        return $code;
    }
    /**
     * 获取验证字段数组值
     * @param string $value
     * @param array  $data
     * @return  object
     */
    public function getVerificationAttr($value, $data)
    {
        $value = array_filter((array)json_decode($value, true));
        $value = array_merge(['email' => 0, 'mobile' => 0], $value);
        return (object)$value;
    }

    /**
     * 设置验证字段
     * @param mixed $value
     * @return string
     */
    public function setVerificationAttr($value)
    {
        $value = is_object($value) || is_array($value) ? json_encode($value) : $value;
        return $value;
    }

    /**
     * 变更会员余额
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function money($money, $user_id, $memo)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $money != 0) {
                $before = $user->money;
                //$after = $user->money + $money;
                $after = function_exists('bcadd') ? bcadd($user->money, $money, 2) : $user->money + $money;
                //更新会员信息
                $user->save(['money' => $after]);
                //写入日志
                MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => $before, 'after' => $after, 'memo' => $memo]);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 变更会员积分
     * @param int    $score   积分
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function score($score, $user_id, $memo)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $score != 0) {
                $before = $user->score;
                $after = $user->score + $score;
                $level = self::nextlevel($after);
                //更新会员信息
                $user->save(['score' => $after]);
                //写入日志
                ScoreLog::create(['user_id' => $user_id, 'score' => $score, 'before' => $before, 'after' => $after, 'memo' => $memo]);

                // 同步到银枣系统
                try {
                    \app\common\service\InzayunBalanceService::syncScore($user_id, $score, $memo, true);
                } catch (\Exception $e) {
                    // 银枣同步失败不影响主流程，记录日志
                    \think\Log::error("银枣积分同步失败: 用户{$user_id}, 积分{$score}, 错误: " . $e->getMessage());
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
        }
    }

    /**
     * 获取用户的银枣会员ID
     * @param int $userId 用户ID
     * @return string|null
     */
    public static function getInzayunMemberId($userId)
    {
        $user = self::get($userId);
        return $user ? $user->third_party_member_id : null;
    }

    /**
     * 根据银枣会员ID获取用户
     * @param string $memberId 银枣会员ID
     * @return User|null
     */
    public static function getByInzayunMemberId($memberId)
    {
        return self::where('third_party_member_id', $memberId)->find();
    }

    /**
     * 绑定银枣会员ID
     * @param int $userId 用户ID
     * @param string $memberId 银枣会员ID
     * @return bool
     */
    public static function bindInzayunMemberId($userId, $memberId)
    {
        $user = self::get($userId);
        if ($user) {
            return $user->save(['third_party_member_id' => $memberId]) !== false;
        }
        return false;
    }

    /**
     * 获取用户的邀请下级列表
     * @param int $userId 用户ID
     * @param int $level 层级（1=直接下级，2=二级下级，0=所有下级）
     * @return array
     */
    public static function getInviteChildren($userId, $level = 1)
    {
        if ($level === 1) {
            // 直接下级
            return self::where('parent', $userId)->select();
        } elseif ($level === 2) {
            // 二级下级
            $directChildren = self::where('parent', $userId)->column('id');
            if (empty($directChildren)) {
                return [];
            }
            return self::where('parent', 'in', $directChildren)->select();
        } else {
            // 所有下级（通过chain字段查询）
            return self::where('chain', 'like', '%,' . $userId . ',%')
                ->whereOr('chain', 'like', $userId . ',%')
                ->whereOr('chain', 'like', '%,' . $userId)
                ->whereOr('chain', $userId)
                ->select();
        }
    }

    /**
     * 获取用户的邀请统计
     * @param int $userId 用户ID
     * @return array
     */
    public static function getInviteStats($userId)
    {
        $directCount = self::where('parent', $userId)->count();
        $directChildren = self::where('parent', $userId)->column('id');
        $indirectCount = 0;

        if (!empty($directChildren)) {
            $indirectCount = self::where('parent', 'in', $directChildren)->count();
        }

        return [
            'direct_count' => $directCount,      // 直接邀请人数
            'indirect_count' => $indirectCount,  // 间接邀请人数
            'total_count' => $directCount + $indirectCount  // 总邀请人数
        ];
    }

    /**
     * 检查邀请码是否有效
     * @param string $inviteCode 邀请码
     * @return User|null
     */
    public static function validateInviteCode($inviteCode)
    {
        if (empty($inviteCode)) {
            return null;
        }
        return self::where('invite_code', $inviteCode)->find();
    }

    public static function setmoney(int $userId,string $coinId,float $money,string $event,string $remark='',float $bbd_rate=0){

        if ($money == 0) {
            return false;
        }
        Db::startTrans();
        try {
            $userInfo = self::where("id",$userId)->lock(true)->find();
            $beforeBalance = $userInfo[$coinId] ?? 0;
            if(($beforeBalance+$money) < 0){
                throw new Exception(__('抱歉，您的余额不足'));
            }
            $change = MoneyLog::create([
                "user_id"   => $userId,
                "coin_id"   => $coinId,
                "money"     => $money,
                "before"    => $beforeBalance,
                "after"     => bcadd($beforeBalance,$money,5),
                "memo"      => $remark,
                "type"      => $event,
            ]);

            if (!$change) {
                throw new \Exception(__('Insert change failed'));
            }
            $return['changeId'] = $change->getLastInsID();
            $userInfo->setInc($coinId,$money);
            DB::commit();
            return $return;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * 变更会员抵扣余额
     * @param float  $amount   抵扣余额变更金额
     * @param int    $user_id  会员ID
     * @param string $memo     备注
     * @param string $type     类型：deduction=抵扣使用,reward=支付赠送,refund=退款返还,admin=管理员操作
     * @param int    $order_id 订单ID
     * @param int    $pay_id   支付ID
     * @param float  $source_amount 来源金额（如实际支付金额）
     * @param float  $percentage 赠送比例
     * @param string $order_no 业务单号（可选，不提供则自动生成）
     * @return bool
     */
    public static function deductionBalance($amount, $user_id, $memo, $type = 'admin', $order_id = null, $pay_id = null, $source_amount = null, $percentage = null, $order_no = null)
    {
        Db::startTrans();
        try {
            $user = self::lock(true)->find($user_id);
            if ($user && $amount != 0) {
                $before = $user->deduction_balance;
                $after = function_exists('bcadd') ? bcadd($user->deduction_balance, $amount, 2) : $user->deduction_balance + $amount;

                // 检查余额是否足够（减少时）
                if ($amount < 0 && $after < 0) {
                    throw new Exception('抵扣余额不足');
                }

                // 准备更新数据
                $updateData = ['deduction_balance' => $after];

                // 如果是获得抵扣余额（reward或admin正数），同步更新累计获得字段
                if (($type == 'reward') || ($type == 'admin' && $amount > 0)) {
                    $beforeTotal = $user->total_earned_deduction_balance ?? 0;
                    $afterTotal = function_exists('bcadd') ? bcadd($beforeTotal, $amount, 2) : $beforeTotal + $amount;
                    $updateData['total_earned_deduction_balance'] = $afterTotal;
                }

                //更新会员信息
                $user->save($updateData);

                //写入普通余额日志（兼容原有系统）
                MoneyLog::create([
                    'user_id' => $user_id,
                    'money' => $amount,
                    'before' => $before,
                    'after' => $after,
                    'memo' => $memo,
                    'balance_type' => 'deduction'
                ]);

                //写入抵扣余额专用日志
                // 如果没有提供单号，自动生成
                if (empty($order_no)) {
                    $order_no = \app\common\service\OrderNumberGenerator::generateDeductionBalanceOrderNo($type, $user_id);
                }

                // 检查单号是否已存在，防止重复创建
                $existingLog = \app\common\model\DeductionBalanceLog::getByOrderNo($order_no, $type);
                if ($existingLog) {
                    throw new \Exception("单号已存在，防止重复操作: {$order_no}");
                }

                Db::name('deduction_balance_log')->insert([
                    'user_id' => $user_id,
                    'order_id' => $order_id,
                    'pay_id' => $pay_id,
                    'type' => $type,
                    'amount' => $amount,
                    'before_balance' => $before,
                    'after_balance' => $after,
                    'source_amount' => $source_amount,
                    'percentage' => $percentage,
                    'memo' => $memo,
                    'order_no' => $order_no,
                    'createtime' => time()
                ]);

                // 同步到银枣系统
                try {
                    \app\common\service\InzayunBalanceService::syncBalance($user_id, $amount, $memo, true);
                } catch (\Exception $e) {
                    // 银枣同步失败不影响主流程，记录日志
                    \think\Log::error("银枣余额同步失败: 用户{$user_id}, 金额{$amount}, 错误: " . $e->getMessage());
                }
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
    }

    /**
     * 获取用户抵扣余额
     * @param int $user_id 会员ID
     * @return float
     */
    public static function getDeductionBalance($user_id)
    {
        $user = self::find($user_id);
        return $user ? $user->deduction_balance : 0;
    }

    /**
     * 计算可用抵扣金额
     * @param int   $user_id     会员ID
     * @param float $order_amount 订单金额
     * @return array [可用抵扣金额, 用户抵扣余额, 最大抵扣比例]
     */
    public static function calculateDeductionAmount($user_id, $order_amount)
    {
        $user = self::find($user_id);
        if (!$user) {
            return [0, 0, 0];
        }

        // 获取系统配置（从zy_config表）
        // 注意：如果配置不存在，默认抵扣比例为0（不允许抵扣），而不是80
        $maxPercentage = self::getConfigValue('balance_deduction_max_percentage', 0);
        $enabled = self::getConfigValue('balance_deduction_enabled', 0);

        if (!$enabled) {
            return [0, $user->deduction_balance, 0];
        }

        // 计算最大可抵扣金额
        $maxDeductionByOrder = bcmul($order_amount, bcdiv($maxPercentage, 100, 4), 2);
        $availableDeduction = min($user->deduction_balance, $maxDeductionByOrder);

        return [$availableDeduction, $user->deduction_balance, $maxPercentage];
    }

    /**
     * 计算支付赠送的抵扣余额
     * @param float $actual_payment 实际支付金额
     * @return float
     */
    public static function calculateRewardAmount($actual_payment)
    {
        // 如果配置不存在，默认赠送比例为0（不赠送）
        $rewardPercentage = self::getConfigValue('balance_reward_percentage', 0);
        return bcmul($actual_payment, bcdiv($rewardPercentage, 100, 4), 2);
    }

    /**
     * 退还抵扣余额（支付失败或取消时使用）
     * @param int   $pay_id 支付ID
     * @param int   $user_id 用户ID
     * @param string $reason 退还原因
     * @return bool
     */
    public static function refundDeductionBalance($pay_id, $user_id, $reason = '支付失败退还')
    {
        // 查找该支付记录的抵扣金额
        $pay_info = Db::name('wanlshop_pay')->where('id', $pay_id)->find();
        if (!$pay_info || $pay_info['deduction_amount'] <= 0) {
            return true; // 没有抵扣金额，直接返回成功
        }

        // 退还抵扣余额
        return self::deductionBalance(
            $pay_info['deduction_amount'],
            $user_id,
            $reason . ':订单' . $pay_info['order_no'],
            'refund',
            $pay_info['order_id'],
            $pay_id
        );
    }

    /**
     * 获取用户累计获得的抵扣余额
     * @param int $user_id 用户ID
     * @return float
     */
    public static function getTotalEarnedDeductionBalance($user_id)
    {
        // 直接从用户表读取累计获得抵扣余额字段
        $user = self::find($user_id);
        return $user ? floatval($user->total_earned_deduction_balance ?? 0) : 0.00;
    }

    /**
     * 获取用户抵扣余额统计信息
     * @param int $user_id 用户ID
     * @return array
     */
    public static function getDeductionBalanceStats($user_id)
    {
        $user = self::find($user_id);
        if (!$user) {
            return [
                'current_balance' => '0.00',
                'total_earned' => '0.00',
                'total_used' => '0.00'
            ];
        }

        // 当前余额（直接从字段读取）
        $currentBalance = $user->deduction_balance ?? '0.00';

        // 累计获得（直接从字段读取）
        $totalEarned = $user->total_earned_deduction_balance ?? '0.00';

        // 累计使用（通过计算得出：累计获得 - 当前余额）
        $totalUsed = function_exists('bcsub') ?
            bcsub($totalEarned, $currentBalance, 2) :
            floatval($totalEarned) - floatval($currentBalance);

        // 确保累计使用不为负数
        $totalUsed = max(0, floatval($totalUsed));

        return [
            'current_balance' => number_format($currentBalance, 2, '.', ''),
            'total_earned' => number_format($totalEarned, 2, '.', ''),
            'total_used' => number_format($totalUsed, 2, '.', '')
        ];
    }

    /**
     * 根据积分获取等级
     * @param int $score 积分
     * @return int
     */
    public static function nextlevel($score = 0)
    {
        $lv = array(1 => 0, 2 => 30, 3 => 100, 4 => 500, 5 => 1000, 6 => 2000, 7 => 3000, 8 => 5000, 9 => 8000, 10 => 10000);
        $level = 1;
        foreach ($lv as $key => $value) {
            if ($score >= $value) {
                $level = $key;
            }
        }
        return $level;
    }

    /**
     * 获取抵扣余额（从银枣数据库同步并更新本地）
     * @param string $value 原始值
     * @param array $data 用户数据
     * @return string 抵扣余额
     */
    public function getDeductionBalanceAttr($value, $data)
    {
        // 如果用户没有绑定银枣会员ID，返回本地数据
        if (empty($data['third_party_member_id'])) {
            return $value ?: '0.00';
        }

        try {
            // 从银枣数据库获取最新余额
            $inzayunBalance = $this->getInzayunUserData($data['third_party_member_id'], 'member_money');

            if ($inzayunBalance !== null) {
                $formattedBalance = number_format($inzayunBalance, 2, '.', '');

                // 检查是否需要更新本地数据
                $currentValue = $value ?: '0.00';
                if ($formattedBalance !== $currentValue) {
                    // 同步更新本地数据库
                    $this->syncUpdateLocalData($data['id'] ?? $this->id, 'deduction_balance', $formattedBalance, $currentValue);
                }

                return $formattedBalance;
            }

            return $value ?: '0.00';
        } catch (\Exception $e) {
            // 银枣数据库查询失败时，返回本地数据
            return $value ?: '0.00';
        }
    }

    /**
     * 获取积分（从银枣数据库同步并更新本地）
     * @param string $value 原始值
     * @param array $data 用户数据
     * @return int 积分
     */
    public function getScoreAttr($value, $data)
    {
        $userId = $data['id'] ?? $this->id ?? 'unknown';
        $mobile = $data['mobile'] ?? 'unknown';

        // 记录开始日志
        \think\Log::info("积分获取器开始执行 - 用户ID:{$userId}, 手机:{$mobile}, 当前积分:{$value}, 银枣会员ID:" . ($data['third_party_member_id'] ?? 'null'));

        // 如果用户没有绑定银枣会员ID，返回本地数据
        if (empty($data['third_party_member_id'])) {
            \think\Log::info("用户未绑定银枣会员ID，返回本地积分 - 用户ID:{$userId}, 积分:" . intval($value));
            return intval($value);
        }

        try {
            // 从银枣数据库获取最新积分
            \think\Log::info("开始查询银枣数据库积分 - 用户ID:{$userId}, 银枣会员ID:{$data['third_party_member_id']}");

            $inzayunPoints = $this->getInzayunUserData($data['third_party_member_id'], 'member_points');

            \think\Log::info("银枣数据库查询结果 - 用户ID:{$userId}, 银枣积分:{$inzayunPoints}, 是否为空:" . ($inzayunPoints === null ? 'true' : 'false'));

            if ($inzayunPoints !== null) {
                $formattedPoints = intval($inzayunPoints);

                // 检查是否需要更新本地数据
                $currentValue = intval($value);

                \think\Log::info("积分数据对比 - 用户ID:{$userId}, 银枣积分:{$inzayunPoints}({$formattedPoints}), 本地积分:{$currentValue}, 需要同步:" . ($formattedPoints !== $currentValue ? 'true' : 'false'));

                if ($formattedPoints !== $currentValue) {
                    \think\Log::info("开始同步积分到本地数据库 - 用户ID:{$userId}, 旧值:{$currentValue}, 新值:{$formattedPoints}");

                    // 同步更新本地数据库
                    $syncResult = $this->syncUpdateLocalData($data['id'] ?? $this->id, 'score', $formattedPoints, $currentValue);

                    \think\Log::info("积分同步完成 - 用户ID:{$userId}, 同步结果:" . ($syncResult ? 'success' : 'failed') . ", 最终积分:{$formattedPoints}");
                } else {
                    \think\Log::info("积分无需同步，数据一致 - 用户ID:{$userId}, 积分:{$formattedPoints}");
                }

                return $formattedPoints;
            }

            \think\Log::warning("银枣数据库返回空值，使用本地积分 - 用户ID:{$userId}, 银枣会员ID:{$data['third_party_member_id']}, 本地积分:" . intval($value));

            return intval($value);
        } catch (\Exception $e) {
            // 银枣数据库查询失败时，返回本地数据
            \think\Log::error("积分获取器异常 - 用户ID:{$userId}, 银枣会员ID:" . ($data['third_party_member_id'] ?? 'null') . ", 错误:" . $e->getMessage() . " 文件:" . $e->getFile() . " 行号:" . $e->getLine());

            return intval($value);
        }
    }

    /**
     * 从银枣数据库获取用户数据
     * @param string $memberId 银枣会员ID
     * @param string $field 要获取的字段名
     * @return mixed 字段值
     */
    private function getInzayunUserData($memberId, $field)
    {
        \think\Log::info("开始查询银枣数据库 - 会员ID:{$memberId}, 字段:{$field}");

        // 缓存键
        // $cacheKey = "inzayun_user_data:{$memberId}:{$field}";

        // 检查缓存
        // $cachedValue = \think\Cache::get($cacheKey);
        // if ($cachedValue !== false) {
        //     return $cachedValue;
        // }

        try {
            // 初始化银枣数据库连接
            \think\Log::info("初始化银枣数据库连接 - 会员ID:{$memberId}, 字段:{$field}");

            \inzayun\DatabaseManager::init();

            // 查询银枣用户数据
            $sql = "SELECT {$field} FROM trader_member WHERE member_id = ? AND is_del = 0";

            \think\Log::info("执行银枣数据库查询 - SQL:{$sql}, 参数:[{$memberId}]");

            $result = \inzayun\DatabaseManager::query($sql, [$memberId]);

            \think\Log::info("银枣数据库查询原始结果 - 会员ID:{$memberId}, 结果数量:" . (is_array($result) ? count($result) : 0) . ", 是否为空:" . (empty($result) ? 'true' : 'false'));

            $value = null;
            if (!empty($result)) {
                $value = $result[0][$field];

                \think\Log::info("提取字段值 - 会员ID:{$memberId}, 字段:{$field}, 值:{$value}, 类型:" . gettype($value));
            } else {
                \think\Log::warning("银枣数据库查询结果为空 - 会员ID:{$memberId}, 字段:{$field}");
            }

            // 缓存结果（5分钟）
            // \think\Cache::set($cacheKey, $value, 300);

            \think\Log::info("银枣数据库查询完成 - 会员ID:{$memberId}, 字段:{$field}, 最终值:{$value}");

            return $value;

        } catch (\Exception $e) {
            // 记录错误日志但不抛出异常
            \think\Log::error("银枣数据库查询失败 - 会员ID:{$memberId}, 字段:{$field}, 错误:" . $e->getMessage() . " 文件:" . $e->getFile() . " 行号:" . $e->getLine());
            return null;
        }
    }

    /**
     * 同步更新本地数据
     * @param int $userId 用户ID
     * @param string $field 字段名
     * @param mixed $newValue 新值
     * @param mixed $oldValue 旧值
     * @return bool 更新是否成功
     */
    private function syncUpdateLocalData($userId, $field, $newValue, $oldValue)
    {
        \think\Log::info("开始同步更新本地数据 - 用户ID:{$userId}, 字段:{$field}, 旧值:{$oldValue}, 新值:{$newValue}");

        try {
            // 使用缓存锁避免并发更新
            $lockKey = "sync_update_lock:{$userId}:{$field}";
            $lockValue = \think\Cache::get($lockKey);

            if ($lockValue) {
                // 已有其他进程在更新，跳过
                \think\Log::warning("检测到并发更新锁，跳过本次更新 - 用户ID:{$userId}, 字段:{$field}, 锁值:{$lockValue}");
                return false;
            }

            // 设置锁（30秒过期）
            \think\Cache::set($lockKey, time(), 30);

            \think\Log::info("获取更新锁成功，开始执行数据库更新 - 用户ID:{$userId}, 字段:{$field}");

            try {
                // 更新本地数据库
                $updateResult = \think\Db::name('user')
                    ->where('id', $userId)
                    ->update([$field => $newValue]);

                \think\Log::info("本地数据库更新完成 - 用户ID:{$userId}, 字段:{$field}, 影响行数:{$updateResult}, 成功:" . ($updateResult > 0 ? 'true' : 'false'));

                // 记录同步日志
                $this->logDataSync($userId, $field, $oldValue, $newValue);

                return ($updateResult > 0);

            } finally {
                // 释放锁
                \think\Cache::rm($lockKey);
                \think\Log::info("释放更新锁 - 用户ID:{$userId}, 字段:{$field}");
            }

        } catch (\Exception $e) {
            // 记录错误日志，但不影响主流程
            \think\Log::error("同步更新本地数据失败 - 用户ID:{$userId}, 字段:{$field}, 错误:" . $e->getMessage() . " 文件:" . $e->getFile() . " 行号:" . $e->getLine());
            return false;
        }
    }

    /**
     * 记录数据同步日志
     * @param int $userId 用户ID
     * @param string $field 字段名
     * @param mixed $oldValue 旧值
     * @param mixed $newValue 新值
     */
    private function logDataSync($userId, $field, $oldValue, $newValue)
    {
        try {
            $logData = [
                'user_id' => $userId,
                'field' => $field,
                'old_value' => $oldValue,
                'new_value' => $newValue,
                'sync_time' => time(),
                'sync_type' => 'getter_auto_sync'
            ];

            // 如果存在同步日志表，记录到表中
            if (\think\Db::query("SHOW TABLES LIKE 'zy_inzayun_balance_sync_log'")) {
                \think\Db::name('inzayun_balance_sync_log')->insert([
                    'user_id' => $userId,
                    'member_id' => '',
                    'changes' => json_encode([$field => ['old' => $oldValue, 'new' => $newValue]], JSON_UNESCAPED_UNICODE),
                    'status' => 'success',
                    'error_message' => '',
                    'sync_time' => time()
                ]);
            } else {
                // 记录到系统日志
                \think\Log::info("银枣数据同步: 用户{$userId} {$field}从{$oldValue}更新为{$newValue}");
            }

        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
            \think\Log::error("记录同步日志失败: " . $e->getMessage());
        }
    }

    /**
     * 清除银枣用户数据缓存
     * @param string $memberId 银枣会员ID
     */
    public static function clearInzayunUserCache($memberId)
    {
        if (empty($memberId)) {
            return;
        }

        $cacheKeys = [
            "inzayun_user_data:{$memberId}:member_money",
            "inzayun_user_data:{$memberId}:member_points"
        ];

        foreach ($cacheKeys as $key) {
            \think\Cache::rm($key);
        }
    }

    /**
     * 从zy_config表获取配置值（直接从数据库读取，不使用缓存）
     * @param string $name 配置名称
     * @param mixed $default 默认值
     * @return mixed 配置值
     */
    public static function getConfigValue($name, $default = null)
    {
        try {
            // 直接从数据库获取配置，不使用缓存
            $configValue = \think\Db::name('config')
                ->where('name', $name)
                ->value('value');

            // 如果配置不存在，使用默认值
            if ($configValue === null) {
                $configValue = $default;
            } else {
                // 尝试转换数据类型
                if (is_numeric($configValue)) {
                    $configValue = strpos($configValue, '.') !== false ? floatval($configValue) : intval($configValue);
                }
            }

            return $configValue;

        } catch (\Exception $e) {
            // 获取配置失败时，返回默认值
            \think\Log::error("获取配置失败: {$name}, 错误: " . $e->getMessage());
            return $default;
        }
    }

    /**
     * 清除配置缓存
     * @param string $name 配置名称，为空时清除所有配置缓存
     */
    public static function clearConfigCache($name = null)
    {
        if ($name) {
            $cacheKey = "config_value:{$name}";
            \think\Cache::rm($cacheKey);
        } else {
            // 清除所有配置缓存（通过前缀匹配）
            $cacheKeys = [
                'config_value:balance_deduction_max_percentage',
                'config_value:balance_deduction_enabled',
                'config_value:balance_reward_percentage'
            ];

            foreach ($cacheKeys as $key) {
                \think\Cache::rm($key);
            }
        }
    }

    /**
     * 强制同步用户银枣数据到本地
     * @param int $userId 用户ID
     * @return array 同步结果
     */
    public static function forceSyncInzayunData($userId)
    {
        try {
            $user = self::get($userId);
            if (!$user || empty($user->third_party_member_id)) {
                return [
                    'success' => false,
                    'message' => '用户不存在或未绑定银枣会员ID'
                ];
            }

            // 清除缓存强制重新获取
            self::clearInzayunUserCache($user->third_party_member_id);

            // 触发获取器，自动同步数据
            $balance = $user->deduction_balance;
            $score = $user->score;

            return [
                'success' => true,
                'message' => '同步成功',
                'data' => [
                    'deduction_balance' => $balance,
                    'score' => $score
                ]
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '同步失败: ' . $e->getMessage()
            ];
        }
    }
}
