<?php

namespace app\api\controller\wanlshop;

use app\common\controller\Api;
use addons\wanlshop\library\EasyWeChat\Easywechat;
use addons\wanlshop\library\WanlChat\WanlChat;
use app\common\model\User as UserMd;
use app\common\library\Sms;
use fast\Random;
use fast\Http;

use think\Db;
use think\Validate;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\exception\HttpResponseException;

/**
 * WanlShop会员接口
 */
class User extends Api
{
    protected $noNeedLogin = [
        "test",
        "login",
        "logout",
        "mobilelogin",
        "register",
        "resetpwd",
        "changeemail",
        "changemobile",
        "third",
        "phone",
        "perfect",
    ];
    protected $noNeedRight = ["*"];

    public function _initialize()
    {
        parent::_initialize();
        //WanlChat 即时通讯调用
        $this->wanlchat = new WanlChat();
        // Auth 写入
        $this->auth->setAllowFields([
            "id",
            "username",
            "nickname",
            "mobile",
            "avatar",
            "level",
            "gender",
            "birthday",
            "bio",
            "money",
            "score",
            "successions",
            "maxsuccessions",
            "prevtime",
            "logintime",
            "loginip",
            "jointime",
            "invite_code",
            "parent",
        ]);
        // Auth 读取
        $this->auth->getAllowFields([
            "id",
            "username",
            "nickname",
            "mobile",
            "avatar",
            "level",
            "gender",
            "birthday",
            "bio",
            "money",
            "score",
            "successions",
            "maxsuccessions",
            "prevtime",
            "logintime",
            "loginip",
            "jointime",
            "invite_code",
            "parent",
        ]);
    }

    /**
     * 会员登录
     * @ApiMethod   (POST)
     * @param string $account  账号
     * @param string $password 密码
     */
    public function login()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $account = $this->request->post("account");
            $password = $this->request->post("password");
            $client_id = $this->request->post("client_id");
            if (!$account || !$password) {
                $this->error(__("Invalid parameters"));
            }
            $ret = $this->auth->login($account, $password);
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }

                $userInfo = self::userInfo();

                // 添加会员码信息
                $memberCodeInfo = $this->getMemberCodeInfo();
                if ($memberCodeInfo) {
                    $userInfo["member_code"] = $memberCodeInfo;
                }

                $this->success(__("Logged in successful"), $userInfo);
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 手机验证码登录
     * @ApiMethod   (POST)
     * @param string $mobile  手机号
     * @param string $captcha 验证码
     */
    public function mobilelogin()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $mobile = $this->request->post("mobile");
            $captcha = $this->request->post("captcha");
            $client_id = $this->request->post("client_id");
            if (!$mobile || !$captcha) {
                $this->error(__("Invalid parameters"));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__("Mobile is incorrect"));
            }
            if (!Sms::check($mobile, $captcha, "mobilelogin")) {
                $this->error(__("Captcha is incorrect"));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if ($user) {
                if ($user->status != "normal") {
                    $this->error(__("Account is locked"));
                }
                //如果已经有账号则直接登录
                $ret = $this->auth->direct($user->id);
            } else {
                $ret = $this->auth->register(
                    $mobile,
                    Random::alnum(),
                    "",
                    $mobile,
                    []
                );
            }
            if ($ret) {
                Sms::flush($mobile, "mobilelogin");
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }

                $userInfo = self::userInfo();

                // 添加会员码信息
                $memberCodeInfo = $this->getMemberCodeInfo();
                if ($memberCodeInfo) {
                    $userInfo["member_code"] = $memberCodeInfo;
                }

                $this->success(__("Logged in successful"), $userInfo);
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 小程序手机号登录
     * @ApiMethod   (POST)
     * @param string $encryptedData
     * @param string $iv
     */
    public function phone()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            try {
                $post = $this->request->post();

                // 记录请求参数，便于调试
                \think\Log::write(
                    "小程序手机号登录请求参数: " .
                        json_encode($post, JSON_UNESCAPED_UNICODE),
                    "info"
                );

                if (!isset($post["code"])) {
                    throw new \Exception("缺少必要参数code");
                }

                if (!isset($post["iv"]) || !isset($post["encryptedData"])) {
                    throw new \Exception("缺少必要参数iv或encryptedData");
                }

                // 1.1.9升级 改为Easywechat
                try {
                    $auth = Easywechat::app()->auth->session($post["code"]);
                    \think\Log::write(
                        "微信登录auth返回: " .
                            json_encode($auth, JSON_UNESCAPED_UNICODE),
                        "info"
                    );
                } catch (\Exception $e) {
                    \think\Log::write(
                        "微信登录auth异常: " . $e->getMessage(),
                        "error"
                    );
                    throw new \Exception("微信登录失败: " . $e->getMessage());
                }

                if (isset($auth["errcode"]) && $auth["errcode"] != 0) {
                    throw new \Exception(
                        "微信登录错误: " .
                            (isset($auth["errmsg"])
                                ? $auth["errmsg"]
                                : "未知错误")
                    );
                }

                if (!isset($auth["openid"]) || !isset($auth["session_key"])) {
                    throw new \Exception(
                        "微信登录返回数据异常，缺少openid或session_key"
                    );
                }

                // 判断third是否存在ID,存在快速登录
                if (isset($auth["unionid"])) {
                    $third = model("app\api\model\wanlshop\Third")->get([
                        "platform" => "mp_weixin",
                        "unionid" => $auth["unionid"],
                    ]);
                    \think\Log::write(
                        "通过unionid查询用户: " . $auth["unionid"],
                        "info"
                    );
                } else {
                    $third = model("app\api\model\wanlshop\Third")->get([
                        "platform" => "mp_weixin",
                        "openid" => $auth["openid"],
                    ]);
                    \think\Log::write(
                        "通过openid查询用户: " . $auth["openid"],
                        "info"
                    );
                }

                \think\Log::write(
                    "查询到third记录: " . ($third ? "是" : "否"),
                    "info"
                );

                //如果已经有账号则直接登录
                $extend = [];
                if (
                    isset($post["invite_code"]) &&
                    !empty($post["invite_code"])
                ) {
                    $extend["invite_code"] = $post["invite_code"];
                    \think\Log::write(
                        "设置邀请码: " . $post["invite_code"],
                        "info"
                    );
                }

                if ($third && $third["user_id"] != 0) {
                    \think\Log::write(
                        "已有账号，直接登录: user_id=" . $third["user_id"],
                        "info"
                    );
                    $ret = $this->auth->direct($third["user_id"], $extend);
                } else {
                    // 手机号解码
                    try {
                        $encryptor = Easywechat::app()->encryptor->decryptData(
                            $auth["session_key"],
                            $post["iv"],
                            $post["encryptedData"]
                        );
                        \think\Log::write(
                            "手机号解码成功: " .
                                json_encode($encryptor, JSON_UNESCAPED_UNICODE),
                            "info"
                        );
                    } catch (\Exception $e) {
                        \think\Log::write(
                            "手机号解码失败: " . $e->getMessage(),
                            "error"
                        );
                        throw new \Exception(
                            "手机号解码失败: " . $e->getMessage()
                        );
                    }

                    if (!isset($encryptor["phoneNumber"])) {
                        throw new \Exception("未能获取手机号信息");
                    }

                    // 开始登录
                    $mobile = $encryptor["phoneNumber"];
                    \think\Log::write("获取到手机号: " . $mobile, "info");

                    $user = \app\common\model\User::getByMobile($mobile);
                    if ($user) {
                        \think\Log::write(
                            "手机号已存在用户: id=" . $user->id,
                            "info"
                        );
                        if ($user->status != "normal") {
                            throw new \Exception("账号已被锁定");
                        }
                        //如果已经有账号则直接登录
                        $ret = $this->auth->direct($user->id, $extend);
                    } else {
                        \think\Log::write("手机号不存在，创建新用户", "info");
                        $extend = [
                            "password" => "123456",
                        ];
                        if (
                            isset($post["invite_code"]) &&
                            !empty($post["invite_code"])
                        ) {
                            $extend["invite_code"] = $post["invite_code"];
                        }
                        $ret = $this->auth->register($mobile, $extend);
                        \think\Log::write(
                            "注册结果: " .
                                ($ret
                                    ? "成功"
                                    : "失败: " . $this->auth->getError()),
                            "info"
                        );
                    }
                }

                if ($ret) {
                    \think\Log::write(
                        "登录成功: user_id=" . $this->auth->id,
                        "info"
                    );
                    if (
                        isset($post["client_id"]) &&
                        $post["client_id"] != null
                    ) {
                        $this->wanlchat->bind(
                            $post["client_id"],
                            $this->auth->id
                        );
                        \think\Log::write(
                            "绑定client_id成功: " . $post["client_id"],
                            "info"
                        );
                    }

                    // 保存或更新用户的微信openid到third表
                    $this->saveUserOpenid(
                        $this->auth->id,
                        $auth["openid"],
                        $auth["session_key"],
                        isset($auth["unionid"]) ? $auth["unionid"] : ""
                    );

                    $userInfo = self::userInfo();

                    // 添加会员码信息
                    $memberCodeInfo = $this->getMemberCodeInfo();
                    if ($memberCodeInfo) {
                        $userInfo["member_code"] = $memberCodeInfo;
                        \think\Log::write(
                            "用户会员码信息: " .
                                json_encode(
                                    $memberCodeInfo,
                                    JSON_UNESCAPED_UNICODE
                                ),
                            "info"
                        );
                    } else {
                        \think\Log::write("用户暂无会员码信息", "info");
                    }

                    \think\Log::write(
                        "返回用户信息: " .
                            json_encode($userInfo, JSON_UNESCAPED_UNICODE),
                        "info"
                    );
                    $this->success(__("Logged in successful"), $userInfo);
                } else {
                    \think\Log::write(
                        "登录失败: " . $this->auth->getError(),
                        "error"
                    );
                    throw new \Exception($this->auth->getError());
                }
            } catch (\think\exception\HttpResponseException $e) {
                // success方法抛出的异常，直接继续抛出，不做处理
                throw $e;
            } catch (\Exception $e) {
                \think\Log::write(
                    "小程序手机号登录异常: " . $e->getMessage(),
                    "error"
                );
                $this->error($e->getMessage());
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 注册会员
     * @ApiMethod   (POST)
     * @param string $mobile   手机号
     * @param string $code   验证码
     */
    public function register()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $mobile = $this->request->post("mobile");
            $code = $this->request->post("captcha");
            $client_id = $this->request->post("client_id");
            if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__("Mobile is incorrect"));
            }
            $ret = Sms::check($mobile, $code, "register");
            if (!$ret) {
                $this->error(__("Captcha is incorrect"));
            }
            $ret = $this->auth->register(
                $mobile,
                Random::alnum(),
                "",
                $mobile,
                []
            );
            if ($ret) {
                if ($client_id) {
                    $this->wanlchat->bind($client_id, $this->auth->id);
                }

                $userInfo = self::userInfo();

                // 添加会员码信息
                $memberCodeInfo = $this->getMemberCodeInfo();
                if ($memberCodeInfo) {
                    $userInfo["member_code"] = $memberCodeInfo;
                }

                $this->success(__("Sign up successful"), $userInfo);
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 注销登录
     */
    public function logout($client_id = null)
    {
        // 踢出即时通讯 1.2.0升级
        /*foreach ($this->wanlchat->getUidToClientId($this->auth->id) as $client_id) {
			$this->wanlchat->destoryClient($client_id);
		}*/
        // 退出登录
        $this->auth->logout();
        $this->success(__("Logout successful"));
    }

    /**
     * 修改会员个人信息
     * @ApiMethod   (POST)
     *
     * @param string $avatar   头像地址
     * @param string $username 用户名
     * @param string $nickname 昵称
     * @param string $bio      个人简介
     */
    public function profile()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $avatar = $this->request->post(
                "avatar",
                "",
                "trim,strip_tags,htmlspecialchars"
            );
            if ($avatar) {
                $user->avatar = $avatar;
            } else {
                $username = $this->request->post("username");
                $nickname = $this->request->post("nickname");
                $bio = $this->request->post("bio");
                // 1.1.9升级 生日和性别并不会提交到后台保存
                $gender = $this->request->post("gender");
                $birthday = $this->request->post(
                    "birthday",
                    $this->auth->birthday
                );
                // 1.1.9升级 优化为Easywechat
                /*if($bio){
					$bioCheck = true;
					try{
					    $security = Easywechat::app()
							->content_security
							->checkText($bio);
						if($security['errcode'] == 87014){
							$bioCheck = false;
						}
					} catch (\Exception $e) {
						$this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：'. $e->getMessage());
					}
					if(!$bioCheck){
						$this->error(__('风控审核：签名包含敏感词汇'));
					}
				}
				if($nickname){
					$nicknameCheck = true;
					try{
					    $security = Easywechat::app()
							->content_security
							->checkText($nickname);
						if($security['errcode'] == 87014){
							$nicknameCheck = false;
						}
					} catch (\Exception $e) {
						$this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：'. $e->getMessage());
					}
					if(!$nicknameCheck){
						$this->error(__('风控审核：昵称包含敏感词汇'));
					}
				}
				if ($username) {
					$usernameCheck = true;
					try{
					    $security = Easywechat::app()
							->content_security
							->checkText($username);
						if($security['errcode'] == 87014){
							$usernameCheck = false;
						}
					} catch (\Exception $e) {
						$this->error('内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：'. $e->getMessage());
					}
					if(!$usernameCheck){
						$this->error(__('风控审核：用户名包含敏感词汇'));
					}
					$exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
					if ($exists) {
						$this->error(__('Username already exists'));
					}
					$user->username = $username;
				}*/
                $user->nickname = $nickname;
                $user->bio = $bio;
                // 1.1.9升级 生日和性别并不会提交到后台保存
                $user->gender = $gender;
                //$user->birthday = $birthday;
            }
            $user->save();
            $this->success("返回成功", $user);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 修改手机号
     * @ApiMethod   (POST)
     * @param string $email   手机号
     * @param string $captcha 验证码
     */
    public function changemobile()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $mobile = $this->request->request("mobile");
            $captcha = $this->request->request("captcha");
            if (!$mobile || !$captcha) {
                $this->error(__("Invalid parameters"));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__("Mobile is incorrect"));
            }
            if (
                \app\common\model\User::where("mobile", $mobile)
                    ->where("id", "<>", $user->id)
                    ->find()
            ) {
                $this->error(__("Mobile already exists"));
            }
            $result = Sms::check($mobile, $captcha, "changemobile");
            if (!$result) {
                $this->error(__("Captcha is incorrect"));
            }
            $verification = $user->verification;
            $verification->mobile = 1;
            $user->verification = $verification;
            $user->mobile = $mobile;
            $user->save();

            Sms::flush($mobile, "changemobile");
            $this->success();
        }
        $this->error(__("非法请求"));
    }

    /**
     * 重置密码
     * @ApiMethod   (POST)
     * @param string $mobile      手机号
     * @param string $newpassword 新密码
     * @param string $captcha     验证码
     */
    public function resetpwd()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $mobile = $this->request->post("mobile");
            $newpassword = $this->request->post("newpassword");
            $captcha = $this->request->post("captcha");
            if (!$newpassword || !$captcha || !$mobile) {
                $this->error(__("Invalid parameters"));
            }
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__("Mobile is incorrect"));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__("User not found"));
            }
            $ret = Sms::check($mobile, $captcha, "resetpwd");
            if (!$ret) {
                $this->error(__("Captcha is incorrect"));
            }
            Sms::flush($mobile, "resetpwd");
            //模拟一次登录
            $this->auth->direct($user->id);
            $ret = $this->auth->changepwd($newpassword, "", true);
            if ($ret) {
                $this->success(__("Reset password successful"));
            } else {
                $this->error($this->auth->getError());
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 第三方登录-web登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     */
    public function third_web()
    {
        $this->error(__("暂未开放"));
    }

    /**
     * 第三方登录
     * @ApiMethod   (POST)
     * @param string $platform 平台名称
     * @param string $code     Code码
     */
    public function third()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            // 获取登录配置
            $config = get_addon_config("wanlshop");
            // 获取前端参数
            $post = $this->request->post();
            // 登录项目
            $time = time();
            $platform = $post["platform"];
            // 开始登录
            switch ($platform) {
                // 微信小程序登录
                case "mp_weixin":
                    // 1.1.9升级 改为Easywechat
                    try {
                        $auth = Easywechat::app()->auth->session(
                            $post["loginData"]["code"]
                        );
                    } catch (\Exception $e) {
                        $this->error($e->getMessage());
                    }
                    if (isset($auth["errcode"])) {
                        $this->error($auth["errmsg"]);
                    }
                    if (isset($auth["unionid"])) {
                        $third = model("app\api\model\wanlshop\Third")->get([
                            "platform" => "weixin_open",
                            "unionid" => $auth["unionid"]
                        ]);
                    } else {
                        $third = model("app\api\model\wanlshop\Third")->get([
                            "platform" => "weixin_open",
                            "openid" => $auth["openid"],
                        ]);
                    }
                    // 成功登录
                    if ($third) {
                        $user = model("app\common\model\User")->get(
                            $third["user_id"]
                        );
                        if (!$user) {
                            $this->success("尚未绑定用户", [
                                "binding" => 0,
                                "token" => $third["token"],
                            ]);
                        }
                        $third->save([
                            "access_token" => $auth["session_key"],
                            "expires_in" => 7776000,
                            "logintime" => $time,
                            "expiretime" => $time + 7776000,
                        ]);
                        $ret = $this->auth->direct($user->id);
                        if ($ret) {
                            if (
                                isset($post["client_id"]) &&
                                $post["client_id"] != null
                            ) {
                                $this->wanlchat->bind(
                                    $post["client_id"],
                                    $this->auth->id
                                );
                            }
                            $this->success(
                                __("Sign up successful"),
                                self::userInfo()
                            );
                        } else {
                            $this->error($this->auth->getError());
                        }
                    } else {
                        // 新增$third
                        $third = model("app\api\model\wanlshop\Third");
                        $third->platform = "weixin_open";
                        if (isset($auth["unionid"])) {
                            $third->unionid = $auth["unionid"];
                        } else {
                            $third->openid = $auth["openid"];
                        }
                        $third->access_token = $auth["session_key"];
                        $third->expires_in = 7776000;
                        $third->logintime = $time;
                        $third->expiretime = $time + 7776000;
                        // 判断当前是否登录
                        if ($this->auth->isLogin()) {
                            if (
                                isset($post["client_id"]) &&
                                $post["client_id"] != null
                            ) {
                                $this->wanlchat->bind(
                                    $post["client_id"],
                                    $this->auth->id
                                );
                            }
                            $third->user_id = $this->auth->id;
                            $third->save();
                            // 直接绑定自动完成
                            $this->success("绑定成功", [
                                "binding" => 1,
                            ]);
                        } else {
                            $third->token = Random::uuid();
                            $third->save();
                            // 通知客户端绑定
                            $this->success("尚未绑定用户", [
                                "binding" => 0,
                                "token" => $third->token,
                            ]);
                        }
                    }
                    break;

                // 微信App登录
                case "app_weixin":
                    $params = [
                        "access_token" =>
                            $post["loginData"]["authResult"]["access_token"],
                        "openid" => $post["loginData"]["authResult"]["openid"],
                    ];
                    $result = Http::sendRequest(
                        "https://api.weixin.qq.com/sns/userinfo",
                        $params,
                        "GET"
                    );
                    if ($result["ret"]) {
                        $json = (array) json_decode($result["msg"], true);
                        if (isset($json["unionid"])) {
                            $third = model("app\api\model\wanlshop\Third")->get(
                                [
                                    "platform" => "weixin_open",
                                    "unionid" => $json["unionid"],
                                ])
                            ;
                        } else {
                            $third = model("app\api\model\wanlshop\Third")->get(
                                [
                                    "platform" => "weixin_open",
                                    "openid" => $json["openid"],
                                ])
                            ;
                        }
                        // 成功登录
                        if ($third) {
                            $third->save([
                                "access_token" =>
                                    $post["loginData"]["authResult"][
                                        "access_token"
                                    ],
                                "refresh_token" =>
                                    $post["loginData"]["authResult"][
                                        "refresh_token"
                                    ],
                                "expires_in" =>
                                    $post["loginData"]["authResult"][
                                        "expires_in"
                                    ],
                                "logintime" => $time,
                                "expiretime" =>
                                    $time +
                                    $post["loginData"]["authResult"][
                                        "expires_in"
                                    ],
                            ]);
                            $ret = $this->auth->direct($third["user_id"]);
                            if ($ret) {
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $this->success(
                                    __("Sign up successful"),
                                    self::userInfo()
                                );
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model("app\api\model\wanlshop\Third");
                            $third->platform = "weixin_open";
                            if (isset($json["unionid"])) {
                                $third->unionid = $json["unionid"];
                            } else {
                                $third->openid = $json["openid"];
                            }
                            $third->access_token =
                                $post["loginData"]["authResult"][
                                    "access_token"
                                ];
                            $third->refresh_token =
                                $post["loginData"]["authResult"][
                                    "refresh_token"
                                ];
                            $third->expires_in =
                                $post["loginData"]["authResult"]["expires_in"];
                            $third->logintime = $time;
                            $third->expiretime =
                                $time +
                                $post["loginData"]["authResult"]["expires_in"];
                            // 判断当前是否登录,否则注册
                            if ($this->auth->isLogin()) {
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success("绑定成功", [
                                    "binding" => 1,
                                ]);
                            } else {
                                $username = $json["nickname"];
                                $auth = [];
                                $mobile = "";
                                $gender = $json["sex"] == 1 ? 1 : 0;
                                $avatar = $json["headimgurl"];
                                // 1.1.3升级
                                if (isset($json["unionid"])) {
                                    // 1.1.3升级 查询其他unionid的user_id进行登录
                                    $unionid = model(
                                        "app\api\model\wanlshop\Third"
                                    )
                                        ->where("user_id", "<>", 0)
                                        ->where(
                                            "unionid",
                                            "=",
                                            $json["unionid"]
                                        )
                                        ->find();
                                    if ($unionid) {
                                        $auth = $this->auth->direct(
                                            $unionid["user_id"]
                                        );
                                    } else {
                                        // 注册账户
                                        $auth = $this->auth->register(
                                            "u_" . Random::alnum(6),
                                            Random::alnum(),
                                            "",
                                            $mobile,
                                            [
                                                "gender" => $gender,
                                                "nickname" => $username,
                                                "avatar" => $avatar,
                                            ]
                                        );
                                    }
                                } else {
                                    // 注册账户
                                    $auth = $this->auth->register(
                                        "u_" . Random::alnum(6),
                                        Random::alnum(),
                                        "",
                                        $mobile,
                                        [
                                            "gender" => $gender,
                                            "nickname" => $username,
                                            "avatar" => $avatar,
                                        ])
                                    ;
                                }
                                if ($auth) {
                                    if (
                                        isset($post["client_id"]) &&
                                        $post["client_id"] != null
                                    ) {
                                        $this->wanlchat->bind(
                                            $post["client_id"],
                                            $this->auth->id
                                        );
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(
                                        __("Sign up successful"),
                                        self::userInfo()
                                    );
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        }
                    } else {
                        $this->error("API异常，App登录失败");
                    }
                    break;
                // 微信公众号登录
                case "h5_weixin":
                    $params = [
                        "appid" => $config["sdk_qq"]["gz_appid"],
                        "secret" => $config["sdk_qq"]["gz_secret"],
                        "code" => $post["code"],
                        "grant_type" => "authorization_code",
                    ];
                    $result = Http::sendRequest(
                        "https://api.weixin.qq.com/sns/oauth2/access_token",
                        $params,
                        "GET"
                    );
                    if ($result["ret"]) {
                        $access = (array) json_decode($result["msg"], true);
                        //获取用户信息
                        $queryarr = [
                            "access_token" => $access["access_token"],
                            "openid" => $access["openid"],
                        ];
                        $ret = Http::sendRequest(
                            "https://api.weixin.qq.com/sns/userinfo",
                            $queryarr,
                            "GET"
                        );
                        if ($ret["ret"]) {
                            $json = (array) json_decode($ret["msg"], true);
                            if (isset($json["unionid"])) {
                                $third = model(
                                    "app\api\model\wanlshop\Third"
                                )->get([
                                    "platform" => "weixin_h5",
                                    "unionid" => $json["unionid"],
                                ]);
                            } else {
                                $third = model(
                                    "app\api\model\wanlshop\Third"
                                )->get([
                                    "platform" => "weixin_h5",
                                    "openid" => $json["openid"],
                                ]);
                            }
                            // 成功登录
                            if ($third) {
                                $third->save([
                                    "openid" => $json["openid"], // 1.1.2升级
                                    "access_token" => $access["access_token"],
                                    "refresh_token" => $access["refresh_token"],
                                    "expires_in" => $access["expires_in"],
                                    "logintime" => $time,
                                    "expiretime" =>
                                        $time + $access["expires_in"],
                                ]);
                                // 登录客户端
                                $ret = $this->auth->direct($third["user_id"]);
                                if ($ret) {
                                    if (
                                        isset($post["client_id"]) &&
                                        $post["client_id"] != null
                                    ) {
                                        $this->wanlchat->bind(
                                            $post["client_id"],
                                            $this->auth->id
                                        );
                                    }
                                    $this->success(
                                        __("Sign up successful"),
                                        self::userInfo()
                                    );
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model("app\api\model\wanlshop\Third");
                                $third->platform = "weixin_h5";
                                // 1.1.2升级
                                if (isset($json["unionid"])) {
                                    $third->unionid = $json["unionid"];
                                    $third->openid = $json["openid"];
                                } else {
                                    $third->openid = $json["openid"];
                                }
                                $third->access_token = $access["access_token"];
                                $third->refresh_token =
                                    $access["refresh_token"];
                                $third->expires_in = $access["expires_in"];
                                $third->logintime = $time;
                                $third->expiretime =
                                    $time + $access["expires_in"];
                                // 获取到的用户信息
                                $username = $json["nickname"];
                                $auth = [];
                                $mobile = "";
                                $gender = $json["sex"] == 1 ? 1 : 0;
                                $avatar = $json["headimgurl"];

                                // 1.1.3升级
                                if (isset($json["unionid"])) {
                                    // 1.1.3升级 查询其他unionid的user_id进行登录
                                    $unionid = model(
                                        "app\api\model\wanlshop\Third"
                                    )
                                        ->where("user_id", "<>", 0)
                                        ->where(
                                            "unionid",
                                            "=",
                                            $json["unionid"]
                                        )
                                        ->find();

                                    if ($unionid) {
                                        $auth = $this->auth->direct(
                                            $unionid["user_id"])
                                        ;
                                    } else {
                                        // 注册账户
                                        $auth = $this->auth->register(
                                            "u_" . Random::alnum(6),
                                            Random::alnum(),
                                            "",
                                            $mobile,
                                            [
                                                "gender" => $gender,
                                                "nickname" => $username,
                                                "avatar" => $avatar,
                                            ])
                                        ;
                                    }
                                } else {
                                    // 注册账户
                                    $auth = $this->auth->register(
                                        "u_" . Random::alnum(6),
                                        Random::alnum(),
                                        "",
                                        $mobile,
                                        [
                                            "gender" => $gender,
                                            "nickname" => $username,
                                            "avatar" => $avatar,
                                        ])
                                    ;
                                }

                                if ($auth) {
                                    if (
                                        isset($post["client_id"]) &&
                                        $post["client_id"] != null
                                    ) {
                                        $this->wanlchat->bind(
                                            $post["client_id"],
                                            $this->auth->id
                                        );
                                    }
                                    // 更新第三方登录
                                    $third->user_id = $this->auth->id;
                                    $third->openname = $username;
                                    $third->save();
                                    $this->success(
                                        __("Sign up successful"),
                                        self::userInfo()
                                    );
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            }
                        } else {
                            $this->error("获取用户信息失败！");
                        }
                    } else {
                        $this->error("获取openid失败！");
                    }
                    break;
                // QQ小程序登录
                case "mp_qq":
                    $params = [
                        "appid" => $config[$platform]["appid"],
                        "secret" => $config[$platform]["appsecret"],
                        "js_code" => $post["loginData"]["code"],
                        "grant_type" => "authorization_code",
                    ];
                    $result = Http::sendRequest(
                        "https://api.q.qq.com/sns/jscode2session",
                        $params,
                        "GET"
                    );
                    if ($result["ret"]) {
                        $json = (array) json_decode($result["msg"], true);
                        if (isset($json["unionid"])) {
                            $third = model("app\api\model\wanlshop\Third")->get(
                                [
                                    "platform" => "qq_open",
                                    "unionid" => $json["unionid"],
                                ])
                            ;
                        } else {
                            $third = model("app\api\model\wanlshop\Third")->get(
                                [
                                    "platform" => "qq_open",
                                    "openid" => $json["openid"],
                                ])
                            ;
                        }
                        // 成功登录
                        if ($third) {
                            $user = model("app\common\model\User")->get(
                                $third["user_id"])
                            ;
                            if (!$user) {
                                $this->success("尚未绑定用户", [
                                    "binding" => 0,
                                    "token" => $third["token"],
                                ]);
                            }
                            $third->save([
                                "access_token" => $json["session_key"],
                                "expires_in" => 7776000,
                                "logintime" => $time,
                                "expiretime" => $time + 7776000,
                            ]);
                            $ret = $this->auth->direct($user->id);
                            if ($ret) {
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $this->success(
                                    __("Sign up successful"),
                                    self::userInfo()
                                );
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model("app\api\model\wanlshop\Third");
                            $third->platform = "qq_open";
                            if (isset($json["unionid"])) {
                                $third->unionid = $json["unionid"];
                            } else {
                                $third->openid = $json["openid"];
                            }
                            $third->access_token = $json["session_key"];
                            $third->expires_in = 7776000;
                            $third->logintime = $time;
                            $third->expiretime = $time + 7776000;
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                // 1.1.4升级
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success("绑定成功", [
                                    "binding" => 1,
                                ]);
                            } else {
                                $third->token = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success("尚未绑定用户", [
                                    "binding" => 0,
                                    "token" => $third->token,
                                ]);
                            }
                        }
                    } else {
                        $this->error("API异常，微信小程序登录失败");
                    }
                    break;

                // QQ App登录
                case "app_qq":
                    $params = [
                        "access_token" =>
                            $post["loginData"]["authResult"]["access_token"],
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            "Content-Type: application/x-www-form-urlencoded",
                        ],
                    ];
                    $result = Http::sendRequest(
                        "https://graph.qq.com/oauth2.0/me",
                        $params,
                        "GET",
                        $options
                    );
                    if ($result["ret"]) {
                        $json = (array) json_decode(
                            str_replace(
                                " );",
                                "",
                                str_replace("callback( ", "", $result["msg"])
                            ),
                            true)
                        ;
                        if (
                            $json["openid"] ==
                            $post["loginData"]["authResult"]["openid"]
                        ) {
                            $third = model("app\api\model\wanlshop\Third")->get(
                                [
                                    "platform" => "qq_open",
                                    "openid" => $json["openid"],
                                ])
                            ;
                            if ($third) {
                                $user = model("app\common\model\User")->get(
                                    $third["user_id"])
                                ;
                                if (!$user) {
                                    $this->success("尚未绑定用户", [
                                        "binding" => 0,
                                        "token" => $third["token"],
                                    ]);
                                }
                                $third->save([
                                    "access_token" =>
                                        $post["loginData"]["authResult"][
                                            "access_token"
                                        ],
                                    "expires_in" =>
                                        $post["loginData"]["authResult"][
                                            "expires_in"
                                        ],
                                    "logintime" => $time,
                                    "expiretime" =>
                                        $time +
                                        $post["loginData"]["authResult"][
                                            "expires_in"
                                        ],
                                ]);
                                $ret = $this->auth->direct($third["user_id"]);
                                if ($ret) {
                                    if (
                                        isset($post["client_id"]) &&
                                        $post["client_id"] != null
                                    ) {
                                        $this->wanlchat->bind(
                                            $post["client_id"],
                                            $this->auth->id
                                        );
                                    }
                                    $this->success(
                                        __("Sign up successful"),
                                        self::userInfo()
                                    );
                                } else {
                                    $this->error($this->auth->getError());
                                }
                            } else {
                                // 新增$third
                                $third = model("app\api\model\wanlshop\Third");
                                $third->platform = "qq_open";
                                $third->openid = $json["openid"];
                                $third->access_token =
                                    $post["loginData"]["authResult"][
                                        "access_token"
                                    ];
                                $third->expires_in =
                                    $post["loginData"]["authResult"][
                                        "expires_in"
                                    ];
                                $third->logintime = $time;
                                $third->expiretime =
                                    $time +
                                    $post["loginData"]["authResult"][
                                        "expires_in"
                                    ];
                                // 判断当前是否登录
                                if ($this->auth->isLogin()) {
                                    if (
                                        isset($post["client_id"]) &&
                                        $post["client_id"] != null
                                    ) {
                                        $this->wanlchat->bind(
                                            $post["client_id"],
                                            $this->auth->id
                                        );
                                    }
                                    $third->user_id = $this->auth->id;
                                    $third->save();
                                    // 直接绑定自动完成
                                    $this->success("绑定成功", [
                                        "binding" => 1,
                                    ]);
                                } else {
                                    $third->token = Random::uuid();
                                    $third->save();
                                    // 通知客户端绑定
                                    $this->success("尚未绑定用户", [
                                        "binding" => 0,
                                        "token" => $third->token,
                                    ]);
                                }
                            }
                        } else {
                            $this->error(__("非法请求，机器信息已提交"));
                        }
                    } else {
                        $this->error("API异常，App登录失败");
                    }
                    break;
                // QQ 网页登录
                case "h5_qq":
                    // 后续版本上线
                    break;
                // 微博App登录
                case "app_weibo":
                    $params = [
                        "access_token" =>
                            $post["loginData"]["authResult"]["access_token"],
                    ];
                    $options = [
                        CURLOPT_HTTPHEADER => [
                            "Content-Type: application/x-www-form-urlencoded",
                        ],
                        CURLOPT_POSTFIELDS => http_build_query($params),
                        CURLOPT_POST => 1,
                    ];
                    $result = Http::post(
                        "https://api.weibo.com/oauth2/get_token_info",
                        $params,
                        $options
                    );
                    $json = (array) json_decode($result, true);
                    if (
                        $json["uid"] == $post["loginData"]["authResult"]["uid"]
                    ) {
                        $third = model("app\api\model\wanlshop\Third")->get([
                            "platform" => "weibo_open",
                            "openid" => $json["uid"],
                        ]);
                        if ($third) {
                            $user = model("app\common\model\User")->get(
                                $third["user_id"])
                            ;
                            if (!$user) {
                                $this->success("尚未绑定用户", [
                                    "binding" => 0,
                                    "token" => $third["token"],
                                ]);
                            }
                            $third->save([
                                "access_token" =>
                                    $post["loginData"]["authResult"][
                                        "access_token"
                                    ],
                                "expires_in" => $json["expire_in"],
                                "logintime" => $json["create_at"],
                                "expiretime" =>
                                    $json["create_at"] + $json["expire_in"],
                            ]);
                            $ret = $this->auth->direct($third["user_id"]);
                            if ($ret) {
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $this->success(
                                    __("Sign up successful"),
                                    self::userInfo()
                                );
                            } else {
                                $this->error($this->auth->getError());
                            }
                        } else {
                            // 新增$third
                            $third = model("app\api\model\wanlshop\Third");
                            $third->platform = "weibo_open";
                            $third->openid = $json["uid"];
                            $third->access_token =
                                $post["loginData"]["authResult"][
                                    "access_token"
                                ];
                            $third->expires_in = $json["expire_in"];
                            $third->logintime = $json["create_at"];
                            $third->expiretime =
                                $json["create_at"] + $json["expire_in"];
                            // 判断当前是否登录
                            if ($this->auth->isLogin()) {
                                // 1.1.4升级
                                if (
                                    isset($post["client_id"]) &&
                                    $post["client_id"] != null
                                ) {
                                    $this->wanlchat->bind(
                                        $post["client_id"],
                                        $this->auth->id
                                    );
                                }
                                $third->user_id = $this->auth->id;
                                $third->save();
                                // 直接绑定自动完成
                                $this->success("绑定成功", [
                                    "binding" => 1,
                                ]);
                            } else {
                                $third->token = Random::uuid();
                                $third->save();
                                // 通知客户端绑定
                                $this->success("尚未绑定用户", [
                                    "binding" => 0,
                                    "token" => $third->token,
                                ]);
                            }
                        }
                    } else {
                        $this->error(__("非法请求，机器信息已提交"));
                    }
                    break;

                // 小米App登录
                case "app_xiaomi":
                    break;

                // 苹果登录
                case "apple":
                    // 后续版本上线
                    break;
                default:
                    $this->error("暂并不支持此方法登录");
            }
        }
        $this->error(__("10086非正常请求"));
    }

    /**
     * 进一步完善资料
     * @ApiMethod   (POST)
     */
    public function perfect()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $post = $this->request->post();

            // 判断token没有绑定 1.1.4升级
            $third = model("app\api\model\wanlshop\Third")
                ->where("token", "=", $post["token"])
                ->find();

            // 当user_id 不为空可以绑定
            if ($third["user_id"] == 0 && $third) {
                $username = $post["nickName"];
                $auth = [];
                $mobile = "";
                $gender = $post["gender"];
                $avatar = $post["avatarUrl"];
                // 1.1.9升级
                if ($username) {
                    $usernameCheck = true;
                    try {
                        $security = Easywechat::app()->content_security->checkText(
                            $username)
                        ;
                        if ($security["errcode"] == 87014) {
                            $usernameCheck = false;
                        }
                    } catch (\Exception $e) {
                        $this->error(
                            "内容审核失败：可能后台小程序的appid、appsecret配置错误，具体：" .
                                $e->getMessage()
                        );
                    }
                    if (!$usernameCheck) {
                        $this->error(__("风控审核：用户名包含敏感词汇"));
                    }
                }
                // 1.1.4升级
                if ($third["unionid"]) {
                    // 1.1.3升级 查询其他unionid的user_id进行登录
                    $unionid = model("app\api\model\wanlshop\Third")
                        ->where("id", "<>", $third["id"])
                        ->where("unionid", "=", $third["unionid"])
                        ->find();
                    if ($unionid) {
                        $auth = $this->auth->direct($unionid["user_id"]);
                    } else {
                        $auth = $this->auth->register($mobile, [
                            "password" => "123456",
                            "gender" => $gender,
                            "nickname" => $username,
                            "avatar" => $avatar,
                        ]);
                    }
                } else {
                    $auth = $this->auth->register($mobile, [
                        "password" => "123456",
                        "gender" => $gender,
                        "nickname" => $username,
                        "avatar" => $avatar,
                    ]);
                }
                if ($auth) {
                    // 1.1.4升级
                    if (
                        isset($post["client_id"]) &&
                        $post["client_id"] != null
                    ) {
                        $this->wanlchat->bind(
                            $post["client_id"],
                            $this->auth->id
                        );
                    }
                    // 更新第三方登录
                    $third->save([
                        "user_id" => $this->auth->id,
                        "openname" => $username,
                    ]);
                    $this->success(__("Sign up successful"), self::userInfo());
                } else {
                    $this->error($this->auth->getError());
                }
            } else {
                $this->error(__("非法请求，机器信息已提交"));
            }
        }
        $this->error(__("非法请求"));
    }

    /**
     * 保存用户微信openid到third表
     * @param int $userId 用户ID
     * @param string $openid 微信openid
     * @param string $sessionKey 微信session_key
     * @param string $unionid 微信unionid
     */
    protected function saveUserOpenid(
        $userId,
        $openid,
        $sessionKey,
        $unionid = ""
    ) {
        try {
            $time = time();

            // 查找是否已存在记录
            $third = model("app\api\model\wanlshop\Third")->get([
                "user_id" => $userId,
                "platform" => "mp_weixin",
            ]);

            if ($third) {
                // 更新现有记录
                $third->save([
                    "openid" => $openid,
                    "access_token" => $sessionKey,
                    "unionid" => $unionid,
                    "expires_in" => 7776000,
                    "logintime" => $time,
                    "expiretime" => $time + 7776000,
                    "updatetime" => $time,
                ]);
            } else {
                // 创建新记录
                $third = model("app\api\model\wanlshop\Third");
                $third->user_id = $userId;
                $third->platform = "mp_weixin";
                $third->openid = $openid;
                $third->access_token = $sessionKey;
                $third->unionid = $unionid;
                $third->expires_in = 7776000;
                $third->logintime = $time;
                $third->expiretime = $time + 7776000;
                $third->createtime = $time;
                $third->updatetime = $time;
                $third->save();
            }

            // 记录日志
            error_log(
                "[WanlShop] 保存用户openid成功: user_id={$userId}, openid={$openid}"
            );
        } catch (\Exception $e) {
            // 记录错误但不影响登录流程
            error_log("[WanlShop] 保存用户openid失败: " . $e->getMessage());
        }
    }

    /**
     * 刷新用户中心
     * @ApiMethod   (POST)
     */
    public function refresh()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $data = self::userInfo();

            // 确保返回完整的余额信息
            // $user = \app\common\model\User::find($this->auth->id);
            // if ($user) {
            //     // 通过模型获取器自动获取最新的银枣余额和积分
            //     $totalEarnedBalance =
            //         $user->total_earned_deduction_balance ?? "0.00";
            //     $currentBalance = $user->deduction_balance; // 通过获取器自动从银枣获取
            //     $currentScore = $user->score; // 通过获取器自动从银枣获取

            //     // 计算累计使用（累计获得 - 当前余额）
            //     $totalUsed = function_exists("bcsub")
            //         ? bcsub($totalEarnedBalance, $currentBalance, 2)
            //         : floatval($totalEarnedBalance) - floatval($currentBalance);
            //     $totalUsed = max(0, $totalUsed);

            //     // 更新用户信息中的余额数据
            //     $data["userinfo"]["money"] = $user->money;
            //     $data["userinfo"]["deduction_balance"] = $currentBalance;
            //     $data["userinfo"][
            //         "total_earned_deduction_balance"
            //     ] = $totalEarnedBalance;
            //     $data["userinfo"]["score"] = $currentScore;

            //     // 添加余额详情到统计信息中
            //     $data["statistics"]["balance"] = [
            //         "money" => $user->money,
            //         "deduction_balance" => $currentBalance,
            //         "total_earned_deduction_balance" => $totalEarnedBalance,
            //         "total_used_deduction_balance" => number_format(
            //             $totalUsed,
            //             2,
            //             ".",
            //             ""
            //         ),
            //         "score" => $currentScore,
            //     ];
            // }

            // 添加会员码信息
            $memberCodeInfo = $this->getMemberCodeInfo();
            if ($memberCodeInfo) {
                $data["member_code"] = $memberCodeInfo;
            }

            $this->success(__("刷新成功"), $data);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 数据统计 - 内部使用，开发者不要调用
     */
    private function userInfo()
    {
        $user_id = $this->auth->id;
        // 查询订单
        $order = model("app\api\model\wanlshop\Order")
            ->where("user_id", $user_id)
            ->where("state", "in", [1, 2, 3, 4, 6])
            ->select();
        $orderCount = array_count_values(array_column($order, "state"));

        // 物流列表
        $logistics = [];
        foreach ($order as $value) {
            if ($value["state"] >= 3 && $value["state"] <= 6) {
                //需要查询的订单
            }
        }
        // 统计数量
        $collection = [];
        $concern = [];
        // 1.1.0升级
        $footgoodsprint = [];
        $footgroupsprint = [];
        foreach (
            model("app\api\model\wanlshop\GoodsFollow")
                ->where("user_id", $user_id)
                ->select()
            as $row
        ) {
            if ($row["goods_type"] === "goods") {
                if (
                    model("app\api\model\wanlshop\Goods")->get($row["goods_id"])
                ) {
                    $collection[] = $row["id"];
                }
            } elseif ($row["goods_type"] === "groups") {
                if (
                    model("app\api\model\wanlshop\groups\Goods")->get(
                        $row["goods_id"]
                    )
                ) {
                    $collection[] = $row["id"];
                }
            }
        }
        // 1.0.8升级  通过uuid查询足迹
        $uuid = $this->request->server("HTTP_UUID");
        if (!isset($uuid)) {
            $charid = strtoupper(
                md5(
                    $this->request->header("user-agent") . $this->request->ip()
                ))
            ;
            $uuid =
                substr($charid, 0, 8) .
                chr(45) .
                substr($charid, 8, 4) .
                chr(45) .
                substr($charid, 12, 4) .
                chr(45) .
                substr($charid, 16, 4) .
                chr(45) .
                substr($charid, 20, 12);
        }
        foreach (
            model("app\api\model\wanlshop\Record")
                ->where("uuid", $uuid)
                ->select()
            as $row
        ) {
            if ($row["goods_type"] === "goods") {
                if (
                    model("app\api\model\wanlshop\Goods")->get($row["goods_id"])
                ) {
                    $footgoodsprint[] = $row["goods_id"];
                }
            } elseif ($row["goods_type"] === "groups") {
                if (
                    model("app\api\model\wanlshop\groups\Goods")->get(
                        $row["goods_id"]
                    )
                ) {
                    $footgroupsprint[] = $row["goods_id"];
                }
            }
        }

        // 查询动态 、收藏夹、关注店铺、足迹、红包卡券
        $finish = isset($orderCount[6]) ? $orderCount[6] : 0;
        $pay = isset($orderCount[1]) ? $orderCount[1] : 0;
        $delive = isset($orderCount[2]) ? $orderCount[2] : 0;
        $receiving = isset($orderCount[3]) ? $orderCount[3] : 0;
        $evaluate = isset($orderCount[4]) ? $orderCount[4] : 0;
        // 订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消
        $groups = model("app\api\model\wanlshop\groups\Order")
            ->where("user_id", "eq", $user_id)
            ->where("state", "neq", 7)
            ->count();
        // 获取用户完整信息，包括余额数据
        $userinfo = $this->auth->getUserinfo();
        $user = \app\common\model\User::find($user_id);

        // 添加余额信息到用户信息中
        if ($user) {
            // 通过模型获取器自动获取最新的银枣余额和积分
            $totalEarnedBalance =
                $user->total_earned_deduction_balance ?? "0.00";
            $currentBalance = $user->deduction_balance; // 通过获取器自动从银枣获取
            $currentScore = $user->score; // 通过获取器自动从银枣获取

            // 计算累计使用（累计获得 - 当前余额）
            $totalUsed = function_exists("bcsub")
                ? bcsub($totalEarnedBalance, $currentBalance, 2)
                : floatval($totalEarnedBalance) - floatval($currentBalance);
            $totalUsed = max(0, $totalUsed);

            // 添加余额字段到用户信息
            $userinfo["money"] = $user->money;
            $userinfo["deduction_balance"] = $currentBalance;
            $userinfo["total_earned_deduction_balance"] = $totalEarnedBalance;
            $userinfo["score"] = $currentScore;
        }

        return [
            "userinfo" => $userinfo,
            "statistics" => [
                "balance" => [
                    "money" => $user ? $user->money : "0.00",
                    "deduction_balance" => $user
                        ? $user->deduction_balance ?? "0.00"
                        : "0.00",
                    "total_earned_deduction_balance" => $user
                        ? $user->total_earned_deduction_balance ?? "0.00"
                        : "0.00",
                    "total_used_deduction_balance" => $user
                        ? number_format($totalUsed, 2, ".", "")
                        : "0.00",
                ],
                "dynamic" => [
                    "collection" => count($collection),
                    "concern" => model('app\api\model\wanlshop\find\Follow')
                        ->where("user_id", $user_id)
                        ->count(),
                    "footprint" =>
                        count(array_flip($footgoodsprint)) +
                        count(array_flip($footgroupsprint)),
                    "coupon" => model("app\api\model\wanlshop\CouponReceive")
                        ->where(["user_id" => $user_id, "state" => "1"])
                        ->count(),
                    "accountbank" => model("app\api\model\wanlshop\PayAccount")
                        ->where("user_id", $user_id)
                        ->count(),
                ],
                "order" => [
                    "whole" =>
                        $finish + $pay + $delive + $receiving + $evaluate,
                    "groups" => $groups,
                    "pay" => $pay,
                    "delive" => $delive,
                    "receiving" => $receiving,
                    "evaluate" => $evaluate,
                    // 1.1.6升级 退款状态:0=申请退款,1=卖家同意,2=卖家拒绝,3=申请平台介入,4=成功退款,5=退款已关闭,6=已提交物流,7=第三方退款中,8=退款失败
                    "customer" => model("app\api\model\wanlshop\Refund")
                        ->whereIn("order_type", ["goods", "groups", "seckill"])
                        ->where([
                            "state" => ["in", "0,1,2,3,6,7,8"],
                            "user_id" => $this->auth->id,
                        ])
                        ->count(),
                ],
                "logistics" => $logistics,
            ],
        ];
    }

    /**
     * 获取用户会员码信息
     * @return array|null
     */
    private function getMemberCodeInfo()
    {
        try {
            if (!$this->auth->isLogin()) {
                return null;
            }

            $userId = $this->auth->id;

            // 获取用户的有效会员码
            $memberCode = \app\common\model\MemberCode::getValidByUserId(
                $userId)
            ;

            if (!$memberCode) {
                // 如果没有会员码，尝试获取
                $this->tryGetMemberCode($userId);
                // 重新查询
                $memberCode = \app\common\model\MemberCode::getValidByUserId(
                    $userId)
                ;
            }

            if ($memberCode) {
                return [
                    "code" => $memberCode->code,
                    "member_id" => $memberCode->member_id,
                    "exp_time" => $memberCode->exp_time,
                    "status" => $memberCode->status,
                    "is_valid" => $memberCode->isValid(),
                    "is_expiring_soon" => $memberCode->isExpiringSoon(24),
                    "remaining_time" => $memberCode->getRemainingTime(),
                    "sync_time" => $memberCode->sync_time,
                ];
            }

            return null;
        } catch (\Exception $e) {
            // 记录错误日志，但不影响主要功能
            \think\Log::error("获取会员码信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 尝试获取用户会员码
     * @param int $userId 用户ID
     */
    private function tryGetMemberCode($userId)
    {
        try {
            // 检查用户是否已同步到银枣
            $user = \app\common\model\User::find($userId);
            if (!$user || empty($user->third_party_member_id)) {
                return;
            }

            // 获取用户Token
            $tokenRecord = \think\Db::name("third_party_token")
                ->where("user_id", $userId)
                ->where("provider", "inzayun")
                ->where("status", "active")
                ->find();

            if (!$tokenRecord || empty($tokenRecord["third_party_token"])) {
                return;
            }

            // 调用用户服务获取会员码
            $userService = new \inzayun\UserService();
            $userService->getMemberCode(
                $userId,
                $user->third_party_member_id,
                $tokenRecord["third_party_token"])
            ;
        } catch (\Exception $e) {
            // 记录错误日志，但不影响主要功能
            \think\Log::error("尝试获取会员码失败: " . $e->getMessage());
        }
    }

    /**
     * 获取评论列表
     *
     * @ApiSummary  (WanlShop 获取我的所有评论)
     * @ApiMethod   (GET)
     *
     * @param string $list_rows  每页数量
     * @param string $page  当前页
     */
    public function comment()
    {
        $list = model("app\api\model\wanlshop\GoodsComment")
            ->where("user_id", $this->auth->id)
            ->field(
                "id,images,score,goods_id,order_goods_id,state,content,createtime"
            )
            ->order("createtime desc")
            ->paginate()
            ->each(function ($data, $key) {
                $data["order_goods"] = $data->order_goods
                    ? $data->order_goods->visible([
                        "id",
                        "title",
                        "image",
                        "price",
                    ])
                    : "";
                return $data;
            });
        $this->success("返回成功", $list);
    }

    /**
     * 获取积分明细
     */
    public function scoreLog()
    {
        //设置过滤方法
        $this->request->filter(["strip_tags"]);
        if ($this->request->isPost()) {
            $list = model("app\common\model\ScoreLog")
                ->where("user_id", $this->auth->id)
                ->order("createtime desc")
                ->paginate();
            $this->success("ok", $list);
        }
        $this->error(__("非法请求"));
    }

    /**
     * 绑定节点码
     */
    public function user_bind()
    {
        $invite_code = $this->request->param("invite_code");
        if ($this->auth->parent) {
            $this->error("您已经绑定");
        }
        $user_info = UserMd::where("invite_code|id", $invite_code)->find();
        if (!$user_info) {
            $this->error("邀请人不存在");
        }
        if ($user_info["id"] == $this->auth->id) {
            $this->error("请勿绑定到自己");
        }
        #检测是不是我的下级
        $check_team = UserMd::where(
            "find_in_set(" . $this->auth->id . ",chain)"
        )
            ->where("id", $user_info["id"])
            ->count();
        if ($check_team) {
            $this->error("无法绑定到此账号");
        }

        Db::startTrans();
        try {
            $res = UserMd::where("id", $this->auth->id)->update([
                "parent" => $user_info["id"],
            ]);
            if (!$res) {
                throw new Exception("绑定失败了");
            }
            $u = UserMd::where("id", $this->auth->id)->find();
            $this->bind($user_info["id"], $u);

            //          给上级赠送3次普通抽奖机会
            $giveDrawNum = Db::name("config")
                ->where("name", "give_draw_num")
                ->value("value");
            Db::name("user_draw_num")
                ->where(["user_id" => $data["parent"] ?? 0])
                ->where("type", 0)
                ->setInc("num", $giveDrawNum);

            Db::commit();
            $this->success("绑定成功");
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * @param $parent_id 父级
     * @param $user_id
     */
    public function bind($parent_id, $user_info)
    {
        //获取新的父节点
        $user_id = $user_info["id"];
        $field = "chain";
        $w = "find_in_set(" . $user_id . ",chain)";
        $parent_info = UserMd::where("id", $parent_id)
            ->field("id,chain,mobile")
            ->find();
        $new_parent_chain = empty($parent_info[$field])
            ? $parent_id
            : $parent_info[$field] . "," . $parent_id;
        UserMd::where("id", $user_id)->update([$field => $new_parent_chain]);
        //更新我的团队关系链
        UserMd::where($w)
            ->field("id,chain")
            ->chunk(100, function ($items) use (
                $new_parent_chain,
                $user_id,
                $field
            ) {
                foreach ($items as $item) {
                    if (strpos($item[$field], (string) $user_id) !== false) {
                        //新父节点+匹配用户id后面的所有id
                        $new_item_chain =
                            $new_parent_chain .
                            "," .
                            strstr($item[$field], (string) $user_id);
                        $item->save([$field => $new_item_chain]);
                    }
                }
            });
    }

    public function test()
    {
        try {
            $order = new \addons\wanlshop\library\command\Order();
            $order->order();
        } catch (\Exception $e) {
            $this->error(
                "订单创建失败：" .
                    $e->getMessage() .
                    "，行" .
                    $e->getLine() .
                    "，文件" .
                    $e->getFile())
            ;
        }

        $this->success("ok");
    }

    /**
     * 获取抵扣余额日志
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认20")
     * @ApiParams   (name="type", type="string", required=false, description="日志类型：deduction=抵扣使用,reward=支付赠送,refund=退款返还,admin=管理员操作")
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "data": {
     *     "list": [
     *       {
     *         "id": 1,
     *         "type": "reward",
     *         "type_text": "支付赠送",
     *         "amount": "5.00",
     *         "before_balance": "0.00",
     *         "after_balance": "5.00",
     *         "source_amount": "100.00",
     *         "percentage": "5.00",
     *         "memo": "订单支付赠送",
     *         "createtime": 1640995200,
     *         "createtime_text": "2022-01-01 12:00:00",
     *         "order_id": 123,
     *         "pay_id": 456
     *       }
     *     ],
     *     "total": 50,
     *     "page": 1,
     *     "limit": 20,
     *     "pages": 3,
     *     "summary": {
     *       "total_earned": "100.00",
     *       "total_used": "50.00",
     *       "current_balance": "50.00"
     *     }
     *   }
     * })
     */
    public function deductionBalanceLogs()
    {
        if ($this->request->isGet()) {
            // 获取请求参数
            $page = $this->request->get("page", 1, "intval");
            $limit = $this->request->get("limit", 20, "intval");
            $type = $this->request->get("type", "", "trim");

            // 参数验证
            $page = max(1, $page);
            $limit = min(100, max(1, $limit)); // 限制每页最多100条

            // 验证类型参数
            $validTypes = ["deduction", "reward", "refund", "admin"];
            if ($type && !in_array($type, $validTypes)) {
                $this->error(__("无效的日志类型"));
            }

            try {
                // 获取当前用户ID
                $userId = $this->auth->id;

                // 构建查询条件
                $where = ["user_id" => $userId];
                if ($type) {
                    $where["type"] = $type;
                }

                // 查询日志列表
                $logModel = new \app\common\model\DeductionBalanceLog();
                $query = $logModel->where($where);

                // 获取总数
                $total = $query->count();

                // 获取列表数据
                $list = $query
                    ->where($where)
                    ->order("createtime desc")
                    ->page($page, $limit)
                    ->select();

                // 格式化数据
                $formattedList = [];
                foreach ($list as $item) {
                    $formattedList[] = [
                        "id" => $item->id,
                        "type" => $item->type,
                        "type_text" => $item->type_text,
                        "amount" => number_format($item->amount, 2, ".", ""),
                        "before_balance" => number_format(
                            $item->before_balance,
                            2,
                            ".",
                            ""
                        ),
                        "after_balance" => number_format(
                            $item->after_balance,
                            2,
                            ".",
                            ""
                        ),
                        "source_amount" => $item->source_amount
                            ? number_format($item->source_amount, 2, ".", "")
                            : null,
                        "percentage" => $item->percentage
                            ? number_format($item->percentage, 2, ".", "")
                            : null,
                        "memo" => $item->memo ?: "",
                        "createtime" => $item->createtime,
                        "createtime_text" => $item->createtime_text,
                        "order_id" => $item->order_id ?: null,
                        "pay_id" => $item->pay_id ?: null,
                    ];
                }

                // 计算页数
                $pages = $total > 0 ? ceil($total / $limit) : 1;

                // 获取用户当前余额信息
                $user = \app\common\model\User::find($userId);
                $currentBalance = $user ? $user->deduction_balance : "0.00";
                $totalEarned = $user
                    ? $user->total_earned_deduction_balance ?? "0.00"
                    : "0.00";

                // 计算累计使用
                $totalUsed = function_exists("bcsub")
                    ? bcsub($totalEarned, $currentBalance, 2)
                    : floatval($totalEarned) - floatval($currentBalance);
                $totalUsed = max(0, $totalUsed);

                // 构建返回数据
                $data = [
                    "list" => $formattedList,
                    "total" => $total,
                    "page" => $page,
                    "limit" => $limit,
                    "pages" => $pages,
                    "summary" => [
                        "total_earned" => number_format(
                            $totalEarned,
                            2,
                            ".",
                            ""
                        ),
                        "total_used" => number_format($totalUsed, 2, ".", ""),
                        "current_balance" => number_format(
                            $currentBalance,
                            2,
                            ".",
                            ""
                        ),
                    ],
                ];

                $this->success(__("获取成功"), $data);
            } catch (HttpResponseException $e) {
                throw $e;
            } catch (\Exception $e) {
                $this->error(__("获取失败：") . $e->getMessage());
            }
        }

        $this->error(__("非法请求"));
    }

    /**
     * 获取抵扣余额统计信息
     * @ApiMethod   (GET)
     * @ApiReturn   ({
     *   "code": 1,
     *   "msg": "获取成功",
     *   "data": {
     *     "current_balance": "50.00",
     *     "total_earned": "100.00",
     *     "total_used": "50.00",
     *     "statistics": {
     *       "reward_count": 10,
     *       "reward_amount": "80.00",
     *       "deduction_count": 5,
     *       "deduction_amount": "30.00",
     *       "refund_count": 1,
     *       "refund_amount": "10.00",
     *       "admin_count": 2,
     *       "admin_amount": "20.00"
     *     }
     *   }
     * })
     */
    public function deductionBalanceStatistics()
    {
        if ($this->request->isGet()) {
            try {
                // 获取当前用户ID
                $userId = $this->auth->id;

                // 获取用户当前余额信息
                $user = \app\common\model\User::find($userId);
                $currentBalance = $user ? $user->deduction_balance : "0.00";
                $totalEarned = $user
                    ? $user->total_earned_deduction_balance ?? "0.00"
                    : "0.00";

                // 计算累计使用
                $totalUsed = function_exists("bcsub")
                    ? bcsub($totalEarned, $currentBalance, 2)
                    : floatval($totalEarned) - floatval($currentBalance);
                $totalUsed = max(0, $totalUsed);

                // 统计各类型的记录数和金额
                $logModel = new \app\common\model\DeductionBalanceLog();
                $statistics = [];

                $types = ["reward", "deduction", "refund", "admin"];
                foreach ($types as $type) {
                    $typeData = $logModel
                        ->where("user_id", $userId)
                        ->where("type", $type)
                        ->field(
                            "COUNT(*) as count, SUM(ABS(amount)) as total_amount"
                        )
                        ->find();

                    $statistics[$type . "_count"] = $typeData["count"] ?: 0;
                    $statistics[$type . "_amount"] = number_format(
                        $typeData["total_amount"] ?: 0,
                        2,
                        ".",
                        "")
                    ;
                }

                // 构建返回数据
                $data = [
                    "current_balance" => number_format(
                        $currentBalance,
                        2,
                        ".",
                        ""
                    ),
                    "total_earned" => number_format($totalEarned, 2, ".", ""),
                    "total_used" => number_format($totalUsed, 2, ".", ""),
                    "statistics" => $statistics,
                ];

                $this->success(__("获取成功"), $data);
            } catch (\Exception $e) {
                $this->error(__("获取失败：") . $e->getMessage());
            }
        }

        $this->error(__("非法请求"));
    }
}
