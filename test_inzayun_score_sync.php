<?php
/**
 * 银枣积分同步测试脚本
 * 测试积分从银枣数据库获取和更新功能
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use app\common\model\User;
use inzayun\DatabaseManager;
use think\Db;

echo "=== 银枣积分同步测试 ===\n\n";

try {
    echo "1. 测试银枣数据库连接...\n";
    
    // 初始化银枣数据库连接
    DatabaseManager::init();
    
    // 测试查询银枣会员数据
    $sql = "SELECT member_id, member_mobile, member_money, member_points FROM trader_member WHERE is_del = 0 AND member_points > 0 LIMIT 5";
    $inzayunMembers = DatabaseManager::query($sql);
    
    echo "✅ 银枣数据库连接成功\n";
    echo "   找到 " . count($inzayunMembers) . " 个有积分的银枣会员\n";
    
    if (!empty($inzayunMembers)) {
        echo "   示例会员数据:\n";
        foreach ($inzayunMembers as $member) {
            echo "     会员ID: {$member['member_id']}, 手机: {$member['member_mobile']}, 余额: {$member['member_money']}, 积分: {$member['member_points']}\n";
        }
    }
    
    echo "\n2. 查找已绑定银枣会员ID的本地用户...\n";
    
    // 查找已绑定银枣会员ID的用户
    $localUsers = Db::name('user')
        ->where('third_party_member_id', '<>', '')
        ->where('third_party_member_id', 'not null')
        ->limit(5)
        ->select();
    
    echo "   找到 " . count($localUsers) . " 个已绑定银枣会员ID的本地用户\n";
    
    if (empty($localUsers)) {
        echo "❌ 没有找到已绑定银枣会员ID的用户，无法测试积分同步\n";
        echo "   请先绑定用户的银枣会员ID\n";
        exit;
    }
    
    echo "\n3. 测试积分获取器功能...\n";
    
    foreach ($localUsers as $userData) {
        echo "   测试用户: ID={$userData['id']}, 手机={$userData['mobile']}, 银枣会员ID={$userData['third_party_member_id']}\n";
        
        // 获取用户模型实例
        $user = User::get($userData['id']);
        if (!$user) {
            echo "     ❌ 用户模型获取失败\n";
            continue;
        }
        
        // 记录原始积分
        $originalScore = $userData['score'] ?? 0;
        echo "     原始本地积分: {$originalScore}\n";
        
        // 直接查询银枣数据库获取积分
        $inzayunSql = "SELECT member_points FROM trader_member WHERE member_id = ? AND is_del = 0";
        $inzayunResult = DatabaseManager::query($inzayunSql, [$userData['third_party_member_id']]);
        
        if (!empty($inzayunResult)) {
            $inzayunScore = $inzayunResult[0]['member_points'];
            echo "     银枣数据库积分: {$inzayunScore}\n";
            
            // 通过获取器获取积分（应该自动同步）
            $syncedScore = $user->score;
            echo "     获取器返回积分: {$syncedScore}\n";
            
            // 检查是否同步成功
            if ($syncedScore == $inzayunScore) {
                echo "     ✅ 积分同步成功\n";
            } else {
                echo "     ❌ 积分同步失败，期望: {$inzayunScore}, 实际: {$syncedScore}\n";
            }
            
            // 检查本地数据库是否已更新
            $updatedUser = Db::name('user')->where('id', $userData['id'])->find();
            $updatedLocalScore = $updatedUser['score'] ?? 0;
            echo "     更新后本地积分: {$updatedLocalScore}\n";
            
            if ($updatedLocalScore == $inzayunScore) {
                echo "     ✅ 本地数据库已同步更新\n";
            } else {
                echo "     ❌ 本地数据库未同步更新\n";
            }
            
        } else {
            echo "     ❌ 银枣数据库中未找到该会员数据\n";
        }
        
        echo "\n";
        break; // 只测试第一个用户
    }
    
    echo "4. 测试积分变更同步...\n";
    
    // 选择一个测试用户
    $testUser = User::get($localUsers[0]['id']);
    if ($testUser && $testUser->third_party_member_id) {
        echo "   测试用户: ID={$testUser->id}, 银枣会员ID={$testUser->third_party_member_id}\n";
        
        // 记录当前积分
        $currentScore = $testUser->score;
        echo "   当前积分: {$currentScore}\n";
        
        // 测试积分变更（增加1分）
        echo "   测试积分增加1分...\n";
        User::score(1, $testUser->id, '测试积分同步');
        
        // 重新获取用户数据
        $testUser = User::get($testUser->id);
        $newScore = $testUser->score;
        echo "   变更后积分: {$newScore}\n";
        
        if ($newScore == ($currentScore + 1)) {
            echo "   ✅ 积分变更成功\n";
        } else {
            echo "   ❌ 积分变更失败\n";
        }
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "   错误文件: " . $e->getFile() . "\n";
    echo "   错误行号: " . $e->getLine() . "\n";
}
