<?php
/**
 * 简单的商品状态检查（直接连接数据库）
 */

echo "=== 简单商品状态检查 ===\n\n";

// 银枣数据库配置
$inzayunConfig = [
    'host' => '**************',
    'port' => 3306,
    'username' => 'dshop_readonly',
    'password' => 'dshop_readonly_2024',
    'database' => 'dshop',
    'charset' => 'utf8mb4'
];

// 本地数据库配置
$localConfig = [
    'host' => '127.0.0.1',
    'port' => 3306,
    'username' => 'root',
    'password' => 'sl331639',
    'database' => 'elecloud',
    'charset' => 'utf8mb4'
];

try {
    // 1. 连接银枣数据库
    echo "1. 连接银枣数据库...\n";
    $inzayunPdo = new PDO(
        "mysql:host={$inzayunConfig['host']};port={$inzayunConfig['port']};dbname={$inzayunConfig['database']};charset={$inzayunConfig['charset']}",
        $inzayunConfig['username'],
        $inzayunConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "   ✅ 银枣数据库连接成功\n";
    
    // 2. 检查银枣商品状态分布
    echo "\n2. 检查银枣商品状态分布...\n";
    $statusSql = "
        SELECT 
            status,
            is_del,
            COUNT(*) as count
        FROM goods_main 
        GROUP BY status, is_del
        ORDER BY status, is_del
    ";
    $stmt = $inzayunPdo->prepare($statusSql);
    $stmt->execute();
    $statusStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   银枣商品状态统计:\n";
    $totalInzayun = 0;
    $onlineInzayun = 0;
    $offlineInzayun = 0;
    
    foreach ($statusStats as $stat) {
        $statusText = $stat['status'] == 1 ? '上架' : '下架';
        $delText = $stat['is_del'] == 0 ? '正常' : '已删除';
        echo "     status={$stat['status']}({$statusText}), is_del={$stat['is_del']}({$delText}): {$stat['count']} 个\n";
        
        $totalInzayun += $stat['count'];
        if ($stat['status'] == 1 && $stat['is_del'] == 0) {
            $onlineInzayun = $stat['count'];
        } else {
            $offlineInzayun += $stat['count'];
        }
    }
    
    echo "   总计: {$totalInzayun} 个商品\n";
    echo "   上架商品: {$onlineInzayun} 个\n";
    echo "   下架/删除商品: {$offlineInzayun} 个\n";
    
    // 3. 查看下架商品示例
    echo "\n3. 查看下架商品示例...\n";
    $offlineGoodsSql = "
        SELECT 
            goods_id,
            goods_name,
            status,
            is_del,
            updatetime
        FROM goods_main 
        WHERE status = 0 OR is_del = 1
        ORDER BY updatetime DESC
        LIMIT 5
    ";
    $stmt = $inzayunPdo->prepare($offlineGoodsSql);
    $stmt->execute();
    $offlineGoods = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($offlineGoods)) {
        echo "   下架/删除商品示例:\n";
        foreach ($offlineGoods as $goods) {
            $statusText = $goods['status'] == 1 ? '上架' : '下架';
            $delText = $goods['is_del'] == 0 ? '正常' : '已删除';
            echo "     ID:{$goods['goods_id']} | {$goods['goods_name']} | {$statusText} | {$delText} | " . date('Y-m-d H:i:s', $goods['updatetime']) . "\n";
        }
    } else {
        echo "   ✅ 没有找到下架或删除的商品\n";
    }
    
    // 4. 连接本地数据库
    echo "\n4. 连接本地数据库...\n";
    $localPdo = new PDO(
        "mysql:host={$localConfig['host']};port={$localConfig['port']};dbname={$localConfig['database']};charset={$localConfig['charset']}",
        $localConfig['username'],
        $localConfig['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    echo "   ✅ 本地数据库连接成功\n";
    
    // 5. 检查本地商品状态分布
    echo "\n5. 检查本地商品状态分布...\n";
    $localStatusSql = "
        SELECT 
            status,
            COUNT(*) as count
        FROM zy_wanlshop_goods 
        WHERE third_party_provider = 'inzayun'
        GROUP BY status
        ORDER BY status
    ";
    $stmt = $localPdo->prepare($localStatusSql);
    $stmt->execute();
    $localStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   本地商品状态统计:\n";
    $totalLocal = 0;
    $onlineLocal = 0;
    $offlineLocal = 0;
    
    foreach ($localStats as $stat) {
        $statusText = $stat['status'] == 'normal' ? '上架' : '下架';
        echo "     {$stat['status']} ({$statusText}): {$stat['count']} 个\n";
        
        $totalLocal += $stat['count'];
        if ($stat['status'] == 'normal') {
            $onlineLocal = $stat['count'];
        } else {
            $offlineLocal = $stat['count'];
        }
    }
    
    echo "   总计: {$totalLocal} 个商品\n";
    echo "   上架商品: {$onlineLocal} 个\n";
    echo "   下架商品: {$offlineLocal} 个\n";
    
    // 6. 对比分析
    echo "\n6. 状态对比分析...\n";
    echo "   数据对比:\n";
    echo "     银枣数据库 - 上架商品: {$onlineInzayun} 个\n";
    echo "     银枣数据库 - 下架商品: {$offlineInzayun} 个\n";
    echo "     本地系统 - 上架商品: {$onlineLocal} 个\n";
    echo "     本地系统 - 下架商品: {$offlineLocal} 个\n";
    
    $onlineDiff = $onlineLocal - $onlineInzayun;
    $offlineDiff = $offlineLocal - $offlineInzayun;
    
    echo "   差异分析:\n";
    echo "     上架商品差异: " . ($onlineDiff >= 0 ? '+' : '') . "{$onlineDiff}\n";
    echo "     下架商品差异: " . ($offlineDiff >= 0 ? '+' : '') . "{$offlineDiff}\n";
    
    // 7. 问题诊断
    echo "\n7. 问题诊断...\n";
    
    if ($offlineDiff < 0) {
        echo "   ❌ 问题确认: 本地系统下架商品数量({$offlineLocal})少于银枣数据库({$offlineInzayun})\n";
        echo "   📋 问题原因: 同步命令只处理上架商品(status=1)，忽略了下架商品的状态更新\n";
        echo "   🔧 解决方案: 修改同步逻辑，同时处理上架和下架商品\n";
    } else {
        echo "   ✅ 状态同步基本正常\n";
    }
    
    // 8. 查看本地系统中应该下架但仍显示上架的商品
    echo "\n8. 查找状态不一致的商品...\n";
    
    // 获取银枣中下架的商品ID
    $offlineIdsSql = "
        SELECT goods_id 
        FROM goods_main 
        WHERE status = 0 OR is_del = 1
        LIMIT 10
    ";
    $stmt = $inzayunPdo->prepare($offlineIdsSql);
    $stmt->execute();
    $offlineIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($offlineIds)) {
        $idsStr = "'" . implode("','", $offlineIds) . "'";
        
        // 检查这些商品在本地系统中的状态
        $localCheckSql = "
            SELECT 
                third_party_id,
                title,
                status,
                updatetime
            FROM zy_wanlshop_goods 
            WHERE third_party_provider = 'inzayun' 
            AND third_party_id IN ({$idsStr})
            AND status = 'normal'
        ";
        $stmt = $localPdo->prepare($localCheckSql);
        $stmt->execute();
        $inconsistentGoods = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($inconsistentGoods)) {
            echo "   ❌ 发现状态不一致的商品:\n";
            foreach ($inconsistentGoods as $goods) {
                echo "     ID:{$goods['third_party_id']} | {$goods['title']} | 本地状态:{$goods['status']} | 更新:" . date('Y-m-d H:i:s', $goods['updatetime']) . "\n";
            }
            echo "   📊 不一致商品数量: " . count($inconsistentGoods) . " 个\n";
        } else {
            echo "   ✅ 抽样检查未发现状态不一致的商品\n";
        }
    }
    
    echo "\n✅ 检查完成！\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}
