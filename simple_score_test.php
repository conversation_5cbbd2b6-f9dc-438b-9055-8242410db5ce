<?php
/**
 * 简单的积分同步测试
 */

echo "=== 银枣积分同步问题诊断 ===\n\n";

try {
    // 1. 测试银枣数据库连接
    echo "1. 测试银枣数据库连接...\n";
    
    $inzayunPdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=dshop;charset=utf8mb4',
        'root',
        'sl331639',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ 银枣数据库连接成功\n";
    
    // 2. 查询银枣会员数据
    echo "\n2. 查询银枣会员数据...\n";
    
    $stmt = $inzayunPdo->query("
        SELECT member_id, member_mobile, member_money, member_points 
        FROM trader_member 
        WHERE is_del = 0 
        ORDER BY member_id DESC 
        LIMIT 5
    ");
    $inzayunMembers = $stmt->fetchAll();
    
    echo "   找到 " . count($inzayunMembers) . " 个银枣会员\n";
    foreach ($inzayunMembers as $member) {
        echo "     会员ID: {$member['member_id']}, 手机: {$member['member_mobile']}, 余额: {$member['member_money']}, 积分: {$member['member_points']}\n";
    }
    
    // 3. 测试本地数据库连接
    echo "\n3. 测试本地数据库连接...\n";
    
    $localPdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=elecloud;charset=utf8mb4',
        'root',
        'sl331639',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ 本地数据库连接成功\n";
    
    // 4. 查询已绑定银枣会员ID的本地用户
    echo "\n4. 查询已绑定银枣会员ID的本地用户...\n";
    
    $stmt = $localPdo->query("
        SELECT id, mobile, score, deduction_balance, third_party_member_id 
        FROM zy_user 
        WHERE third_party_member_id IS NOT NULL 
        AND third_party_member_id != '' 
        LIMIT 5
    ");
    $localUsers = $stmt->fetchAll();
    
    echo "   找到 " . count($localUsers) . " 个已绑定的本地用户\n";
    
    if (empty($localUsers)) {
        echo "❌ 没有找到已绑定银枣会员ID的用户\n";
        echo "   建议：手动绑定一个用户的银枣会员ID进行测试\n";
        exit;
    }
    
    foreach ($localUsers as $user) {
        echo "     用户ID: {$user['id']}, 手机: {$user['mobile']}, 积分: {$user['score']}, 余额: {$user['deduction_balance']}, 银枣会员ID: {$user['third_party_member_id']}\n";
    }
    
    // 5. 测试数据同步
    echo "\n5. 测试数据同步...\n";
    
    $testUser = $localUsers[0];
    $memberId = $testUser['third_party_member_id'];
    
    echo "   测试用户: ID={$testUser['id']}, 银枣会员ID={$memberId}\n";
    
    // 查询银枣数据库中的用户数据
    $stmt = $inzayunPdo->prepare("
        SELECT member_money, member_points 
        FROM trader_member 
        WHERE member_id = ? AND is_del = 0
    ");
    $stmt->execute([$memberId]);
    $inzayunData = $stmt->fetch();
    
    if ($inzayunData) {
        echo "   银枣数据库: 余额={$inzayunData['member_money']}, 积分={$inzayunData['member_points']}\n";
        echo "   本地数据库: 余额={$testUser['deduction_balance']}, 积分={$testUser['score']}\n";
        
        // 检查数据是否一致
        $balanceMatch = ($inzayunData['member_money'] == $testUser['deduction_balance']);
        $scoreMatch = ($inzayunData['member_points'] == $testUser['score']);
        
        echo "\n   数据一致性检查:\n";
        echo "     余额一致: " . ($balanceMatch ? "✅" : "❌") . "\n";
        echo "     积分一致: " . ($scoreMatch ? "✅" : "❌") . "\n";
        
        if (!$balanceMatch) {
            echo "     余额差异: 银枣={$inzayunData['member_money']}, 本地={$testUser['deduction_balance']}\n";
        }
        
        if (!$scoreMatch) {
            echo "     积分差异: 银枣={$inzayunData['member_points']}, 本地={$testUser['score']}\n";
        }
        
    } else {
        echo "❌ 银枣数据库中未找到会员ID为 {$memberId} 的数据\n";
    }
    
    echo "\n=== 诊断完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . "\n";
    echo "   行号: " . $e->getLine() . "\n";
}
