<?php
/**
 * 详细调试数据库连接
 */

// 设置环境变量
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/base.php';

// 初始化应用
\think\App::initCommon();

echo "=== 详细数据库连接调试 ===\n\n";

$testMemberId = 31828;

try {
    echo "1. 检查DatabaseManager配置...\n";
    
    // 获取DatabaseManager的配置
    $config = \inzayun\DatabaseManager::getConfig();
    echo "   DatabaseManager配置:\n";
    echo "     hostname: {$config['inzayun_readonly']['hostname']}\n";
    echo "     database: {$config['inzayun_readonly']['database']}\n";
    echo "     username: {$config['inzayun_readonly']['username']}\n";
    echo "     password: " . substr($config['inzayun_readonly']['password'], 0, 3) . "***\n";
    echo "     hostport: {$config['inzayun_readonly']['hostport']}\n\n";
    
    echo "2. 测试DatabaseManager连接...\n";
    
    // 初始化DatabaseManager
    \inzayun\DatabaseManager::init();
    $connection = \inzayun\DatabaseManager::getConnection();
    
    echo "   ✅ DatabaseManager连接成功\n";
    echo "   连接类型: " . get_class($connection) . "\n";
    
    // 获取原生PDO连接
    $pdo = $connection->getPdo();
    echo "   PDO连接类型: " . get_class($pdo) . "\n\n";
    
    echo "3. 测试基本连接信息...\n";
    
    // 查询当前数据库信息
    $stmt = $pdo->query("SELECT DATABASE() as current_db, USER() as current_user, @@hostname as hostname");
    $dbInfo = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    echo "   当前数据库: {$dbInfo['current_db']}\n";
    echo "   当前用户: {$dbInfo['current_user']}\n";
    echo "   主机名: {$dbInfo['hostname']}\n\n";
    
    echo "4. 检查表是否存在...\n";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'trader_member'");
    $tableExists = $stmt->fetch();
    
    if ($tableExists) {
        echo "   ✅ trader_member表存在\n";
        
        // 检查表记录数
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM trader_member");
        $count = $stmt->fetch(\PDO::FETCH_ASSOC);
        echo "   表记录数: {$count['count']}\n";
        
    } else {
        echo "   ❌ trader_member表不存在\n";
    }
    echo "\n";
    
    echo "5. 检查用户权限...\n";
    
    $stmt = $pdo->query("SHOW GRANTS");
    $grants = $stmt->fetchAll(\PDO::FETCH_COLUMN);
    
    echo "   用户权限:\n";
    foreach ($grants as $grant) {
        echo "     {$grant}\n";
    }
    echo "\n";
    
    echo "6. 测试具体查询...\n";
    
    // 测试查询特定会员
    $sql = "SELECT member_id, member_mobile, member_points, is_del FROM trader_member WHERE member_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$testMemberId]);
    $result = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "   查询SQL: {$sql}\n";
    echo "   参数: [{$testMemberId}]\n";
    echo "   结果数量: " . count($result) . "\n";
    echo "   结果内容: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    if (empty($result)) {
        echo "7. 查找相似的会员ID...\n";
        
        $stmt = $pdo->query("SELECT member_id, member_mobile, member_points FROM trader_member ORDER BY member_id DESC LIMIT 5");
        $recentMembers = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        echo "   最近的5个会员:\n";
        foreach ($recentMembers as $member) {
            echo "     ID: {$member['member_id']}, 手机: {$member['member_mobile']}, 积分: {$member['member_points']}\n";
        }
        echo "\n";
        
        // 查找包含31828的会员ID
        $stmt = $pdo->prepare("SELECT member_id, member_mobile, member_points FROM trader_member WHERE member_id LIKE ? LIMIT 5");
        $stmt->execute(['%31828%']);
        $similarMembers = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        if (!empty($similarMembers)) {
            echo "   包含31828的会员ID:\n";
            foreach ($similarMembers as $member) {
                echo "     ID: {$member['member_id']}, 手机: {$member['member_mobile']}, 积分: {$member['member_points']}\n";
            }
        } else {
            echo "   没有找到包含31828的会员ID\n";
        }
    }
    
    echo "\n=== 调试完成 ===\n";
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . "\n";
    echo "   行号: " . $e->getLine() . "\n";
}
