<?php
/**
 * 银枣数据库连接配置
 * 注意：此配置仅用于只读查询，不允许任何写操作
 */

return [
    // 银枣数据库连接配置（只读）
    "inzayun_readonly" => [
        "type" => "mysql",
        "hostname" => "127.0.0.1",
        "database" => "dshop",
        "username" => "root",
        "password" => "sl331639",
        "hostport" => "3306",
        "charset" => "utf8mb4",
        "prefix" => "",
        "debug" => false,
        "deploy" => 0,
        "rw_separate" => false,
        "master_num" => 1,
        "slave_no" => "",
        "fields_strict" => true,
        "resultset_type" => "array",
        "auto_timestamp" => false,
        "datetime_format" => "Y-m-d H:i:s",
        "sql_explain" => false,
        // 连接池配置
        "pool" => [
            "max_connections" => 10,
            "min_connections" => 2,
            "connection_timeout" => 30,
            "idle_timeout" => 600,
        ],
        // MySQL连接参数
        "params" => [
            \PDO::ATTR_TIMEOUT => 30,
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            \PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4",
            \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            \PDO::ATTR_PERSISTENT => false,
        ],
        // 只读模式强制配置
        "readonly" => true,
        "allowed_operations" => ["SELECT", "SHOW", "DESCRIBE", "EXPLAIN"],
    ],

    // 查询配置
    "query_config" => [
        // 批量查询大小
        "batch_size" => 1000,
        // 查询超时时间（秒）
        "query_timeout" => 60,
        // 最大重试次数
        "max_retries" => 5,
        // 重试间隔（秒）
        "retry_interval" => 2,
        // 连接超时时间（秒）
        "connection_timeout" => 60,
    ],

    // 同步配置
    "sync_config" => [
        // 同步模式：database, api, mixed
        "default_mode" => "database",
        // 数据转换配置
        "transform_config" => [
            "validate_data" => true,
            "skip_invalid" => true,
            "log_errors" => true,
        ],
        // 性能配置
        "performance" => [
            "enable_cache" => true,
            "cache_ttl" => 300, // 5分钟
            "enable_compression" => false,
        ],
    ],

    // 表映射配置
    "table_mapping" => [
        // 银枣表 => 本地表
        "trader_shop" => "wanlshop_shop",
        "goods_type" => "wanlshop_category",
        "goods_main" => "wanlshop_goods",
        "trader_member" => "wanlshop_user",
        "order" => "wanlshop_order",
    ],

    // 字段映射配置
    "field_mapping" => [
        "trader_shop" => [
            "shop_id" => "third_party_id",
            "trader_id" => "third_party_trader_id",
            "shop_code" => "shop_code",
            "shop_name" => "shopname",
            "shop_address" => "address",
            "shop_tel" => "phone",
            "longitude" => "lng",
            "latitude" => "lat",
            "starting_delivery" => "starting_delivery",
            "status" => "status",
            "addtime" => "createtime",
        ],
        "goods_type" => [
            "type_id" => "third_party_id",
            "trader_id" => "third_party_trader_id",
            "type_p_id" => "pid",
            "type_name" => "name",
            "type_sort" => "weigh",
            "status" => "status",
            "addtime" => "createtime",
        ],
        "goods_main" => [
            "goods_id" => "third_party_id",
            "trader_id" => "third_party_trader_id",
            "goods_name" => "title",
            "goods_price" => "price",
            "goods_member_price" => "member_price",
            "goods_thumb" => "image",
            "goods_code" => "goods_code",
            "goods_barcode" => "goods_barcode",
            "unit_name" => "unit",
            "status" => "status",
            "addtime" => "createtime",
        ],
    ],

    // 安全配置
    "security" => [
        // 禁止的SQL关键词
        "forbidden_keywords" => [
            "INSERT",
            "UPDATE",
            "DELETE",
            "DROP",
            "CREATE",
            "ALTER",
            "TRUNCATE",
            "REPLACE",
            "MERGE",
            "CALL",
            "EXECUTE",
        ],
        // 允许的表前缀
        "allowed_table_prefixes" => ["trader_", "goods_", "order", "commerce_"],
        // 查询日志
        "enable_query_log" => true,
        "log_slow_queries" => true,
        "slow_query_threshold" => 2, // 秒
    ],
];
