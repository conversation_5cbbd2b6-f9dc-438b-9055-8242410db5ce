<?php
/**
 * 测试积分获取器的日志功能
 */

// 设置环境变量
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/base.php';

// 初始化应用
\think\App::initCommon();

// 设置日志级别为info，确保所有日志都能记录
\think\Log::init([
    'type' => 'File',
    'path' => RUNTIME_PATH . 'log/',
    'level' => ['info', 'notice', 'warning', 'error', 'debug'],
    'file_size' => 2097152,
    'time_format' => 'Y-m-d H:i:s'
]);

use app\common\model\User;

echo "=== 积分获取器日志测试 ===\n\n";

try {
    // 1. 查找一个已绑定银枣会员ID的用户
    echo "1. 查找测试用户...\n";
    
    $testUser = User::where('third_party_member_id', '<>', '')
        ->where('third_party_member_id', 'not null')
        ->find();
    
    if (!$testUser) {
        echo "❌ 未找到已绑定银枣会员ID的用户\n";
        exit;
    }
    
    echo "   找到测试用户: ID={$testUser->id}, 手机={$testUser->mobile}, 银枣会员ID={$testUser->third_party_member_id}\n";
    echo "   当前本地积分: {$testUser->getData('score')}\n\n";
    
    // 2. 在银枣数据库中设置一个不同的积分值
    echo "2. 设置银枣数据库测试积分...\n";
    
    $testPoints = 88; // 设置一个特殊的积分值便于识别
    
    $inzayunPdo = new PDO(
        'mysql:host=127.0.0.1;port=3306;dbname=dshop;charset=utf8mb4',
        'root',
        'sl331639',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $inzayunPdo->prepare("UPDATE trader_member SET member_points = ? WHERE member_id = ? AND is_del = 0");
    $stmt->execute([$testPoints, $testUser->third_party_member_id]);
    
    echo "   已设置银枣会员ID {$testUser->third_party_member_id} 的积分为 {$testPoints}\n\n";
    
    // 3. 清空之前的日志（可选）
    echo "3. 清理旧日志...\n";
    $logPath = RUNTIME_PATH . 'log/' . date('Ymd') . '.log';
    if (file_exists($logPath)) {
        // 备份旧日志
        $backupPath = RUNTIME_PATH . 'log/' . date('Ymd') . '_backup_' . time() . '.log';
        copy($logPath, $backupPath);
        // 清空当前日志
        file_put_contents($logPath, '');
        echo "   已备份并清空日志文件: {$logPath}\n";
    }
    echo "\n";
    
    // 4. 触发积分获取器
    echo "4. 触发积分获取器（这会产生详细日志）...\n";
    
    // 重新获取用户实例以确保数据是最新的
    $testUser = User::get($testUser->id);
    
    // 调用积分获取器
    $score = $testUser->score;
    
    echo "   积分获取器返回: {$score}\n";
    echo "   预期积分: {$testPoints}\n";
    echo "   同步结果: " . ($score == $testPoints ? "✅ 成功" : "❌ 失败") . "\n\n";
    
    // 5. 显示日志内容
    echo "5. 查看生成的日志...\n";
    
    if (file_exists($logPath)) {
        $logContent = file_get_contents($logPath);
        if (!empty($logContent)) {
            echo "   日志文件: {$logPath}\n";
            echo "   日志内容:\n";
            echo "   " . str_repeat("-", 80) . "\n";
            
            // 只显示最近的日志（包含我们的测试）
            $lines = explode("\n", $logContent);
            $relevantLines = [];
            $found = false;
            
            foreach ($lines as $line) {
                if (strpos($line, '积分获取器开始执行') !== false) {
                    $found = true;
                }
                if ($found && !empty(trim($line))) {
                    $relevantLines[] = $line;
                }
            }
            
            if (!empty($relevantLines)) {
                foreach ($relevantLines as $line) {
                    echo "   " . $line . "\n";
                }
            } else {
                echo "   未找到相关的积分同步日志\n";
                echo "   完整日志内容:\n";
                echo "   " . $logContent . "\n";
            }
            
            echo "   " . str_repeat("-", 80) . "\n";
        } else {
            echo "   ❌ 日志文件为空\n";
        }
    } else {
        echo "   ❌ 日志文件不存在: {$logPath}\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    echo "请检查上面的日志输出，查看积分同步的详细过程。\n";
    echo "如果日志显示正常但积分仍未同步，可能的原因：\n";
    echo "1. 数据库连接配置问题\n";
    echo "2. 银枣数据库权限问题\n";
    echo "3. 缓存锁机制问题\n";
    echo "4. 数据库事务问题\n";
    
} catch (Exception $e) {
    echo "❌ 测试过程中发生错误: " . $e->getMessage() . "\n";
    echo "   错误文件: " . $e->getFile() . "\n";
    echo "   错误行号: " . $e->getLine() . "\n";
}
