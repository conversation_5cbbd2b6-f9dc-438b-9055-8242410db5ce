<?php

namespace inzayun;

use think\Db;
use think\Exception;
use think\Config;

/**
 * 银枣数据库连接管理器
 * 专门用于管理银枣数据库的只读连接
 */
class DatabaseManager
{
    /**
     * 数据库连接实例
     * @var \think\db\Connection
     */
    private static $connection = null;

    /**
     * 配置信息
     * @var array
     */
    private static $config = null;

    /**
     * 查询日志
     * @var array
     */
    private static $queryLog = [];

    /**
     * 初始化数据库连接
     * @return void
     * @throws Exception
     */
    public static function init()
    {
        if (self::$connection !== null) {
            return;
        }

        // 加载配置
        self::$config = self::loadConfig();
        if (empty(self::$config)) {
            throw new Exception('银枣数据库配置未找到');
        }

        // 获取重试配置
        $maxRetries = self::$config['query_config']['max_retries'] ?? 3;
        $retryInterval = self::$config['query_config']['retry_interval'] ?? 1;

        $lastException = null;

        // 重试连接
        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                // 创建数据库连接
                self::$connection = Db::connect(self::$config['inzayun_readonly']);

                // 测试连接
                self::testConnection();

                // 连接成功，记录日志
                if ($attempt > 1) {
                    self::logConnectionEvent("银枣数据库连接成功（第{$attempt}次尝试）");
                }

                return;

            } catch (Exception $e) {
                $lastException = $e;
                self::$connection = null;

                self::logConnectionEvent("银枣数据库连接失败（第{$attempt}次尝试）: " . $e->getMessage());

                // 如果不是最后一次尝试，等待后重试
                if ($attempt < $maxRetries) {
                    sleep($retryInterval);
                }
            }
        }

        // 所有重试都失败了
        throw new Exception('银枣数据库连接失败（已重试' . $maxRetries . '次）: ' . $lastException->getMessage());
    }

    /**
     * 获取数据库连接
     * @return \think\db\Connection
     * @throws Exception
     */
    public static function getConnection()
    {
        if (self::$connection === null) {
            self::init();
        }
        return self::$connection;
    }

    /**
     * 执行只读查询
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array
     * @throws Exception
     */
    public static function query($sql, $params = [])
    {
        // 安全检查
        self::validateQuery($sql);
        
        $startTime = microtime(true);
        
        try {
            $connection = self::getConnection();

            // 添加调试日志
            \think\Log::info("DatabaseManager执行查询 - SQL:{$sql}, 参数:" . json_encode($params, JSON_UNESCAPED_UNICODE));

            $result = $connection->query($sql, $params);

            // 添加详细的返回值调试
            \think\Log::info("DatabaseManager原始返回值 - 类型:" . gettype($result) . ", 内容:" . json_encode($result, JSON_UNESCAPED_UNICODE));

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            // 记录查询日志
            self::logQuery($sql, $params, $duration, is_array($result) ? count($result) : 0);

            return $result;
            
        } catch (Exception $e) {
            self::logQuery($sql, $params, 0, 0, $e->getMessage());
            throw new Exception('查询执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取表数据（分页）
     * @param string $table 表名
     * @param array $where 条件
     * @param array $fields 字段
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $order 排序
     * @return array
     */
    public static function getTableData($table, $where = [], $fields = ['*'], $page = 1, $limit = 1000, $order = '')
    {
        try {
            $connection = self::getConnection();
            
            $query = $connection->table($table);
            
            // 添加条件
            if (!empty($where)) {
                $query = $query->where($where);
            }
            
            // 添加字段
            if ($fields !== ['*']) {
                $query = $query->field($fields);
            }
            
            // 添加排序
            if (!empty($order)) {
                $query = $query->order($order);
            }
            
            // 分页
            $offset = ($page - 1) * $limit;
            $result = $query->limit($offset, $limit)->select();
            
            return $result ?: [];
            
        } catch (Exception $e) {
            throw new Exception("获取表 {$table} 数据失败: " . $e->getMessage());
        }
    }

    /**
     * 获取表总数
     * @param string $table 表名
     * @param array $where 条件
     * @return int
     */
    public static function getTableCount($table, $where = [])
    {
        try {
            $connection = self::getConnection();
            
            $query = $connection->table($table);
            
            if (!empty($where)) {
                $query = $query->where($where);
            }
            
            return $query->count();
            
        } catch (Exception $e) {
            throw new Exception("获取表 {$table} 总数失败: " . $e->getMessage());
        }
    }

    /**
     * 验证查询安全性
     * @param string $sql SQL语句
     * @throws Exception
     */
    private static function validateQuery($sql)
    {
        $sql = strtoupper(trim($sql));
        
        // 检查是否为只读操作
        if (!preg_match('/^(SELECT|SHOW|DESCRIBE|EXPLAIN)/', $sql)) {
            throw new Exception('只允许执行只读查询操作');
        }
        
        // 检查禁止的关键词
        $forbiddenKeywords = self::$config['security']['forbidden_keywords'] ?? [];
        foreach ($forbiddenKeywords as $keyword) {
            if (strpos($sql, $keyword) !== false) {
                throw new Exception("禁止使用关键词: {$keyword}");
            }
        }
    }

    /**
     * 测试数据库连接
     * @throws Exception
     */
    private static function testConnection()
    {
        try {
            $result = self::$connection->query('SELECT 1 as test');
            if (empty($result) || $result[0]['test'] != 1) {
                throw new Exception('连接测试失败');
            }
        } catch (Exception $e) {
            throw new Exception('数据库连接测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 记录查询日志
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param float $duration 执行时间(ms)
     * @param int $rows 返回行数
     * @param string $error 错误信息
     */
    private static function logQuery($sql, $params, $duration, $rows, $error = '')
    {
        $logEntry = [
            'sql' => $sql,
            'params' => $params,
            'duration' => $duration,
            'rows' => $rows,
            'error' => $error,
            'time' => date('Y-m-d H:i:s'),
        ];

        self::$queryLog[] = $logEntry;

        // 如果启用了详细日志，打印SQL信息
        if (self::isVerboseLoggingEnabled()) {
            self::printQueryLog($sql, $params, $duration, $rows, $error);
        }

        // 记录慢查询
        $slowThreshold = self::$config['security']['slow_query_threshold'] ?? 2;
        if ($duration > $slowThreshold * 1000) {
            // echo "⚠️  慢查询警告: {$duration}ms - {$sql}\n"; // Removed echo
        }

        // 记录错误
        if (!empty($error)) {
            // echo "❌ 查询错误: {$error} - {$sql}\n"; // Removed echo
        }
    }

    /**
     * 检查是否启用详细日志
     * @return bool
     */
    private static function isVerboseLoggingEnabled()
    {
        // 检查环境变量
        if (getenv('INZAYUN_VERBOSE_SQL') === 'true') {
            return true;
        }

        // 检查配置
        return self::$config['security']['enable_query_log'] ?? false;
    }

    /**
     * 打印SQL查询日志
     * @param string $sql SQL语句
     * @param array $params 参数
     * @param float $duration 执行时间(ms)
     * @param int $rows 返回行数
     * @param string $error 错误信息
     */
    private static function printQueryLog($sql, $params, $duration, $rows, $error = '')
    {
        // 日志记录到文件而不是直接输出
        if (function_exists('write_log')) {
            $formattedSql = self::formatSqlForDisplay($sql, $params);
            $logMessage = "[SQL查询] " . date('H:i:s') . "\n";
            $logMessage .= "SQL: {$formattedSql}\n";
            
            if (!empty($params)) {
                $logMessage .= "参数: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";
            }
            
            if (empty($error)) {
                $logMessage .= "结果: {$rows} 行, 耗时: {$duration}ms\n";
            } else {
                $logMessage .= "错误: {$error}\n";
            }
            
            write_log($logMessage, 'sql_debug');
        }
    }

    /**
     * 格式化SQL语句用于显示
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return string
     */
    private static function formatSqlForDisplay($sql, $params = [])
    {
        $formattedSql = $sql;

        // 替换参数占位符
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $placeholder = is_numeric($key) ? '?' : ":{$key}";
                $replacement = is_string($value) ? "'{$value}'" : $value;
                $formattedSql = str_replace($placeholder, $replacement, $formattedSql);
            }
        }

        // 简化多行SQL为单行显示
        $formattedSql = preg_replace('/\s+/', ' ', trim($formattedSql));

        return $formattedSql;
    }

    /**
     * 获取查询日志
     * @return array
     */
    public static function getQueryLog()
    {
        return self::$queryLog;
    }

    /**
     * 清空查询日志
     */
    public static function clearQueryLog()
    {
        self::$queryLog = [];
    }

    /**
     * 启用SQL详细日志输出
     * @param bool $enabled 是否启用
     */
    public static function enableVerboseLogging($enabled = true)
    {
        if (!isset(self::$config['security'])) {
            self::$config['security'] = [];
        }
        self::$config['security']['enable_query_log'] = $enabled;
    }

    /**
     * 打印查询统计信息
     */
    public static function printQueryStats()
    {
        $totalQueries = count(self::$queryLog);
        if ($totalQueries === 0) {
            return;
        }

        $totalDuration = array_sum(array_column(self::$queryLog, 'duration'));
        $totalRows = array_sum(array_column(self::$queryLog, 'rows'));
        $avgDuration = round($totalDuration / $totalQueries, 2);

        // 记录到日志文件而不是直接输出
        if (function_exists('write_log')) {
            $message = "查询统计信息:\n";
            $message .= "  总查询数: {$totalQueries}\n";
            $message .= "  总耗时: {$totalDuration}ms\n";
            $message .= "  平均耗时: {$avgDuration}ms\n";
            $message .= "  总返回行数: {$totalRows}\n";
            
            write_log($message, 'sql_stats');
        }
    }

    /**
     * 关闭数据库连接
     */
    public static function close()
    {
        if (self::$connection !== null) {
            self::$connection = null;
            // echo "🔌 银枣数据库连接已关闭\n"; // Removed echo
        }
    }

    /**
     * 加载配置
     * @return array
     * @throws Exception
     */
    private static function loadConfig()
    {
        // 方式1：尝试从ThinkPHP Config加载
        $config = Config::get('inzayun_database');
        if (!empty($config)) {
            return $config;
        }

        // 方式2：直接加载配置文件
        $configFile = APP_PATH . '../config/inzayun_database.php';
        if (!file_exists($configFile)) {
            throw new Exception("配置文件不存在: {$configFile}");
        }

        try {
            // 临时定义env函数（如果不存在）
            if (!function_exists('env')) {
                function env($key, $default = null) {
                    $value = getenv($key);
                    if ($value === false) {
                        return $default;
                    }
                    return $value;
                }
            }

            $config = include $configFile;
            if (!is_array($config)) {
                throw new Exception('配置文件格式错误');
            }

            // 手动设置到ThinkPHP Config中
            Config::set($config, 'inzayun_database');

            return $config;

        } catch (Exception $e) {
            throw new Exception("配置文件加载失败: " . $e->getMessage());
        }
    }

    /**
     * 获取配置信息
     * @param string $key 配置键
     * @return mixed
     */
    public static function getConfig($key = '')
    {
        if (self::$config === null) {
            self::$config = self::loadConfig();
        }

        if (empty($key)) {
            return self::$config;
        }

        return self::$config[$key] ?? null;
    }

    /**
     * 记录连接事件日志
     * @param string $message 日志消息
     */
    private static function logConnectionEvent($message)
    {
        // 记录到ThinkPHP日志系统
        if (class_exists('\think\Log')) {
            \think\Log::info('[银枣数据库] ' . $message);
        }

        // 如果启用详细日志，也输出到控制台
        if (self::isVerboseLoggingEnabled()) {
            echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        }
    }

    /**
     * 诊断网络连接
     * @return array 诊断结果
     */
    public static function diagnoseConnection()
    {
        $config = self::$config['inzayun_readonly'] ?? [];
        $hostname = $config['hostname'] ?? '';
        $port = $config['hostport'] ?? 3306;

        $results = [
            'hostname' => $hostname,
            'port' => $port,
            'ping_test' => false,
            'telnet_test' => false,
            'dns_resolution' => false,
            'mysql_connect' => false,
            'error_messages' => []
        ];

        // 1. DNS解析测试
        try {
            $ip = gethostbyname($hostname);
            if ($ip !== $hostname) {
                $results['dns_resolution'] = true;
                $results['resolved_ip'] = $ip;
            } else {
                $results['error_messages'][] = "DNS解析失败: 无法解析主机名 {$hostname}";
            }
        } catch (Exception $e) {
            $results['error_messages'][] = "DNS解析异常: " . $e->getMessage();
        }

        // 2. 端口连通性测试
        try {
            $socket = @fsockopen($hostname, $port, $errno, $errstr, 10);
            if ($socket) {
                $results['telnet_test'] = true;
                fclose($socket);
            } else {
                $results['error_messages'][] = "端口连接失败: {$hostname}:{$port} - {$errstr} (错误码: {$errno})";
            }
        } catch (Exception $e) {
            $results['error_messages'][] = "端口测试异常: " . $e->getMessage();
        }

        // 3. MySQL连接测试
        try {
            $dsn = "mysql:host={$hostname};port={$port};charset=utf8mb4";
            $pdo = new \PDO($dsn, $config['username'] ?? '', $config['password'] ?? '', [
                \PDO::ATTR_TIMEOUT => 10,
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION
            ]);
            $results['mysql_connect'] = true;
            $pdo = null;
        } catch (\PDOException $e) {
            $results['error_messages'][] = "MySQL连接失败: " . $e->getMessage();
        }

        return $results;
    }
}
