<?php

namespace inzayun;

use think\Config;
use think\Db;

/**
 * 银枣数据库数据转换器
 * 将银枣数据库的数据转换为本地系统格式
 */
class DatabaseTransformer
{
    /**
     * 配置信息
     * @var array
     */
    protected $config;

    /**
     * 字段映射配置
     * @var array
     */
    protected $fieldMapping;

    public function __construct()
    {
        $this->config = Config::get('inzayun_database');
        $this->fieldMapping = $this->config['field_mapping'] ?? [];
    }

    /**
     * 转换门店数据
     * @param array $shopData 银枣门店数据
     * @return array
     */
    public function transformShop($shopData)
    {
        $mapping = $this->fieldMapping['trader_shop'] ?? [];

        // 获取手机号（优先使用shop_mobile，其次shop_tel）
        $phoneNumber = $shopData['shop_mobile'] ?? $shopData['shop_tel'] ?? '';

        $transformed = [
            'third_party_id' => $shopData['shop_id'] ?? '',
            'third_party_provider' => 'inzayun',
            'third_party_trader_id' => $shopData['trader_id'] ?? '',
            'shopname' => $shopData['shop_name'] ?? '',
            'shop_code' => $shopData['shop_code'] ?? '',
            'address' => $shopData['shop_address'] ?? '',
            'phone' => $phoneNumber, // 同步到phone字段
            'shop_tel' => $phoneNumber, // 同步到shop_tel字段
            'contact_phone' => $phoneNumber, // 同步到contact_phone字段
            'lng' => $this->formatCoordinate($shopData['longitude'] ?? 0),
            'lat' => $this->formatCoordinate($shopData['latitude'] ?? 0),
            'starting_delivery' => $this->formatPrice($shopData['starting_delivery'] ?? 0),
            'status' => $this->mapShopStatus($shopData['status'] ?? 1),
            'business_hours' => $this->buildBusinessHours($shopData),
            'third_party_data' => json_encode($shopData, JSON_UNESCAPED_UNICODE),
            'data_source' => 'database',
            'last_sync_time' => time(),
            'createtime' => $this->formatTime($shopData['addtime'] ?? time()),
            'updatetime' => time()
        ];

        return $this->filterEmptyFields($transformed);
    }

    /**
     * 转换品牌数据
     * @param array $brandData 银枣品牌数据
     * @return array 本地品牌数据
     */
    public function transformBrand($brandData)
    {
        return [
            // 基本信息
            'name' => $brandData['brand_name'] ?? '',
            'shop_id' => $this->getShopIdByTraderId($brandData['trader_id'] ?? 0),
            'admin_id' => 0, // 系统同步，管理员ID为0
            'category_id' => '', // 品牌不绑定特定分类

            // 品牌内容
            'image' => '', // 银枣品牌表没有图片字段，留空
            'content' => $this->buildBrandContent($brandData),

            // 状态和权重
            'status' => $this->mapBrandStatus($brandData['status'] ?? 1),
            'state' => $this->mapBrandState($brandData['status'] ?? 1),
            'switch' => $this->mapBrandSwitch($brandData['status'] ?? 1),
            'weigh' => intval($brandData['brand_id'] ?? 0),

            // 第三方信息
            'third_party_provider' => 'inzayun',
            'third_party_id' => $brandData['brand_id'] ?? 0,
            'third_party_data' => json_encode($brandData, JSON_UNESCAPED_UNICODE),
            'data_source' => 'third_party',
            'last_sync_time' => time(),
        ];
    }

    /**
     * 转换分类数据
     * @param array $categoryData 银枣分类数据
     * @return array
     */
    public function transformCategory($categoryData)
    {
        $transformed = [
            'third_party_id' => $categoryData['type_id'] ?? '',
            'third_party_provider' => 'inzayun',
            'third_party_trader_id' => $categoryData['trader_id'] ?? '',
            'pid' => $this->mapCategoryPid($categoryData['type_p_id'] ?? 0),
            'name' => $categoryData['type_name'] ?? '',
            'image' => $this->processImage($categoryData['type_img'] ?? ''),
            'weigh' => intval($categoryData['type_sort'] ?? 0),
            'status' => $this->mapCategoryStatus($categoryData['status'] ?? 1),
            'third_party_data' => json_encode($categoryData, JSON_UNESCAPED_UNICODE),
            'data_source' => 'database',
            'last_sync_time' => time(),
            'createtime' => $this->formatTime($categoryData['addtime'] ?? time()),
            'updatetime' => time()
        ];

        return $this->filterEmptyFields($transformed);
    }

    /**
     * 转换混合商品数据（goods_main + 电商商品信息）
     * @param array $goodsData 银枣混合商品数据
     * @return array
     */
    public function transformGoods($goodsData)
    {
        $transformed = [
            // 第三方追踪信息 - 使用goods_main的goods_id
            'third_party_id' => $goodsData['goods_id'] ?? '',
            'third_party_provider' => 'inzayun',
            'third_party_trader_id' => $goodsData['trader_id'] ?? '',

            // 关联信息 - 映射到本地系统
            'shop_id' => $this->mapInzayunShopId($goodsData['final_shop_id'] ?? $goodsData['shop_id'] ?? '', $goodsData),
            'category_id' => $this->mapCategoryId($goodsData['final_type_id'] ?? $goodsData['type_id'] ?? ''),
            'brand_id' => $this->mapBrandId($goodsData['brand_id'] ?? 0),

            // 商品基本信息 - 使用goods_main字段
            'title' => $goodsData['goods_name'] ?? '',
            'description' => $this->buildHybridGoodsDescription($goodsData),
            'content' => $this->buildHybridGoodsContent($goodsData),

            // 图片信息 - 使用混合图片处理，过滤默认图片
            'images' => $this->buildHybridGoodsImages($goodsData),
            'image' => $this->getMainImageFromImages($this->buildHybridGoodsImages($goodsData)),

            // 价格信息 - 使用最终价格
            'price' => $this->formatPrice($goodsData['final_price'] ?? $goodsData['goods_price'] ?? 0),
            'market_price' => $this->formatPrice($goodsData['goods_purchase_price'] ?? 0),
            'member_price' => $this->formatPrice($goodsData['final_member_price'] ?? $goodsData['goods_member_price'] ?? 0),

            // 商品状态和标识 - 使用本地系统字段
            'flag' => $this->mapHybridGoodsFlag($goodsData),
            'grounding' => $this->mapGoodsGrounding($goodsData['status'] ?? 1),
            'specs' => 'single', // 默认单规格，SKU表中处理多规格
            'distribution' => 'false', // 默认不开启分销
            'activity' => '0', // 默认不在活动中
            'activity_id' => 0,
            'activity_type' => 'goods',

            // 库存计算方式 - 使用本地系统枚举
            'stock' => 'porder', // 下单减库存

            // 权重排序 - 使用最终排序值
            'weigh' => intval($goodsData['final_sort'] ?? $goodsData['sort'] ?? 0),

            // 状态 - 使用电商商品状态而不是主表状态
            'status' => $this->mapCommerceGoodsStatus($goodsData['commerce_status'] ?? $goodsData['status'] ?? 1),

            // 商品参数 - 基于品牌信息生成
            'parameter' => $this->buildGoodsParameter($goodsData),

            // 系统信息
            'third_party_data' => json_encode($goodsData, JSON_UNESCAPED_UNICODE),
            'data_source' => 'third_party',
            'last_sync_time' => time(),
            'createtime' => $this->formatTime($goodsData['addtime'] ?? time()),
            'updatetime' => time()
        ];

        return $this->filterEmptyFields($transformed);
    }

    /**
     * 映射门店状态
     * @param int $status 银枣门店状态
     * @return string
     */
    protected function mapShopStatus($status)
    {
        return intval($status) === 1 ? 'normal' : 'hidden';
    }

    /**
     * 映射分类状态
     * @param int $status 银枣分类状态
     * @return string
     */
    protected function mapCategoryStatus($status)
    {
        return intval($status) === 1 ? 'normal' : 'hidden';
    }

    /**
     * 映射商品状态（使用goods_main表状态）
     * @param int $status 银枣商品状态
     * @return string
     */
    protected function mapGoodsStatus($status)
    {
        return intval($status) === 1 ? 'normal' : 'hidden';
    }

    /**
     * 映射电商商品状态（使用commerce_goods_detail表状态）
     * @param int $commerceStatus 电商商品状态 (1=上架, 2=下架)
     * @return string
     */
    protected function mapCommerceGoodsStatus($commerceStatus)
    {
        return intval($commerceStatus) === 1 ? 'normal' : 'hidden';
    }

    /**
     * 映射商品上架状态
     * @param int $status 银枣商品状态
     * @return int
     */
    protected function mapGoodsGrounding($status)
    {
        return intval($status) === 1 ? 1 : 0;
    }

    /**
     * 映射分类父级ID
     * @param int $thirdPartyPid 银枣分类父级ID
     * @return int
     */
    protected function mapCategoryPid($thirdPartyPid)
    {
        if (empty($thirdPartyPid) || $thirdPartyPid == 0) {
            return 0;
        }

        // 查找对应的本地分类ID
        try {
            $category = \think\Db::name('wanlshop_category')
                ->where('third_party_id', $thirdPartyPid)
                ->where('third_party_provider', 'inzayun')
                ->find();
            
            return $category ? $category['id'] : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 映射门店ID
     * @param string $traderId 银枣商户ID
     * @return int
     */
    protected function mapShopId($traderId)
    {
        try {
            // 获取该商户的第一个门店
            $shop = \think\Db::name('wanlshop_shop')
                ->where('third_party_trader_id', $traderId)
                ->where('third_party_provider', 'inzayun')
                ->order('id ASC')
                ->find();
            
            return $shop ? $shop['id'] : 1; // 默认门店ID为1
        } catch (\Exception $e) {
            return 1;
        }
    }

    /**
     * 映射分类ID
     * @param string $thirdPartyCategoryId 银枣分类ID
     * @return int
     */
    protected function mapCategoryId($thirdPartyCategoryId)
    {
        if (empty($thirdPartyCategoryId)) {
            return $this->getDefaultCategoryId();
        }

        try {
            $category = \think\Db::name('wanlshop_category')
                ->where('third_party_id', $thirdPartyCategoryId)
                ->where('third_party_provider', 'inzayun')
                ->find();

            return $category ? $category['id'] : $this->getDefaultCategoryId();
        } catch (\Exception $e) {
            return $this->getDefaultCategoryId();
        }
    }

    /**
     * 获取默认分类ID
     * @return int
     */
    protected function getDefaultCategoryId()
    {
        try {
            // 查找现有的默认分类
            $defaultCategory = \think\Db::name('wanlshop_category')
                ->where('name', '默认分类')
                ->where('type', 'goods')
                ->find();

            if ($defaultCategory) {
                return $defaultCategory['id'];
            }

            // 如果没有找到，创建默认分类（只使用必要字段）
            $categoryData = [
                'pid' => 0,
                'name' => '默认分类',
                'type' => 'goods',
                'status' => 'normal',
                'weigh' => 0,
                'createtime' => time(),
                'updatetime' => time()
            ];

            $categoryId = \think\Db::name('wanlshop_category')->insertGetId($categoryData);

            if ($categoryId) {
                echo "✅ 创建默认分类: 默认分类 (ID: {$categoryId})\n";
                return $categoryId;
            }

            // 如果创建失败，返回1作为兜底
            return 1;

        } catch (\Exception $e) {
            echo "❌ 获取默认分类失败: " . $e->getMessage() . "\n";
            return 1; // 兜底返回ID为1
        }
    }

    /**
     * 映射品牌ID
     * @param int $thirdPartyBrandId 银枣品牌ID
     * @return int
     */
    protected function mapBrandId($thirdPartyBrandId)
    {
        if (empty($thirdPartyBrandId) || $thirdPartyBrandId == 0) {
            return 0;
        }

        // 查找本地品牌ID
        $localBrand = Db::name('wanlshop_brand')
            ->where('third_party_provider', 'inzayun')
            ->where('third_party_id', $thirdPartyBrandId)
            ->field('id')
            ->find();

        return $localBrand ? intval($localBrand['id']) : 0;
    }

    /**
     * 根据trader_id获取shop_id
     * @param int $traderId 商户ID
     * @return int
     */
    protected function getShopIdByTraderId($traderId)
    {
        if (empty($traderId) || $traderId == 0) {
            return 0;
        }

        // 查找本地门店ID
        $localShop = Db::name('wanlshop_shop')
            ->where('third_party_provider', 'inzayun')
            ->where('third_party_trader_id', $traderId)
            ->field('id')
            ->find();

        return $localShop ? intval($localShop['id']) : 0;
    }

    /**
     * 构建商品描述
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function buildGoodsDescription($goodsData)
    {
        $description = [];

        // 商品昵称
        if (!empty($goodsData['goods_nick'])) {
            $description[] = $goodsData['goods_nick'];
        }

        // 品牌信息
        if (!empty($goodsData['brand_name'])) {
            $description[] = "品牌：" . $goodsData['brand_name'];
        }

        // 规格信息
        if (!empty($goodsData['goods_size'])) {
            $description[] = "规格：" . $goodsData['goods_size'];
        }

        // 单位信息
        if (!empty($goodsData['unit_name'])) {
            $description[] = "单位：" . $goodsData['unit_name'];
        }

        return implode(' | ', $description);
    }

    /**
     * 构建商品图片
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function buildGoodsImages($goodsData)
    {
        $images = [];

        // 主图
        if (!empty($goodsData['thumb'])) {
            $images[] = $this->processImage($goodsData['thumb']);
        }

        // 如果有额外的图片数据，可以在这里处理
        // 例如从 goods_picture 表获取的图片
        if (!empty($goodsData['picture_urls'])) {
            foreach ($goodsData['picture_urls'] as $url) {
                $processedUrl = $this->processImage($url);
                if (!empty($processedUrl) && !in_array($processedUrl, $images)) {
                    $images[] = $processedUrl;
                }
            }
        }

        return implode(',', array_filter($images));
    }



    /**
     * 映射商品标识
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function mapGoodsFlag($goodsData)
    {
        $flags = [];

        // 根据价格判断是否为特价商品
        $price = floatval($goodsData['goods_price'] ?? 0);
        $purchasePrice = floatval($goodsData['goods_purchase_price'] ?? 0);

        if ($purchasePrice > 0 && $price < $purchasePrice) {
            $flags[] = 'promotion';
        }

        // 根据会员折扣判断
        $memberDiscount = intval($goodsData['member_discount'] ?? 1);
        if ($memberDiscount == 1) {
            $flags[] = 'recommend';
        }

        // 根据积分设置判断
        $allowPoints = intval($goodsData['allow_points'] ?? 0);
        if ($allowPoints == 1) {
            $flags[] = 'quality';
        }

        // 根据促销设置判断
        $allowPromo = intval($goodsData['allow_promo'] ?? 0);
        if ($allowPromo == 1) {
            $flags[] = 'hot';
        }

        return implode(',', array_unique($flags));
    }

    /**
     * 构建营业时间
     * @param array $shopData 门店数据
     * @return string
     */
    protected function buildBusinessHours($shopData)
    {
        $startTime = $shopData['business_start_time'] ?? '09:00';
        $endTime = $shopData['business_end_time'] ?? '21:00';
        
        return "{$startTime}-{$endTime}";
    }

    /**
     * 构建商品详情
     * @param array $goodsData 商品数据
     * @return string
     */
    protected function buildGoodsContent($goodsData)
    {
        $content = [];

        // 添加商品描述
        if (!empty($goodsData['goods_desc'])) {
            $content[] = "<p>" . htmlspecialchars($goodsData['goods_desc']) . "</p>";
        }

        // 添加品牌信息
        if (!empty($goodsData['brand_name'])) {
            $content[] = "<p><strong>品牌：</strong>" . htmlspecialchars($goodsData['brand_name']) . "</p>";
        }

        // 添加规格信息
        if (!empty($goodsData['goods_size'])) {
            $content[] = "<p><strong>规格：</strong>" . htmlspecialchars($goodsData['goods_size']) . "</p>";
        }

        // 添加单位信息
        if (!empty($goodsData['unit_name'])) {
            $content[] = "<p><strong>单位：</strong>" . htmlspecialchars($goodsData['unit_name']) . "</p>";
        }

        // 添加条码信息
        if (!empty($goodsData['goods_barcode']) || !empty($goodsData['barcode_code'])) {
            $barcode = $goodsData['goods_barcode'] ?: $goodsData['barcode_code'];
            $content[] = "<p><strong>条码：</strong>" . htmlspecialchars($barcode) . "</p>";
        }

        // 如果没有内容，添加默认内容
        if (empty($content)) {
            $content[] = "<p>商品详情</p>";
        }

        return implode("\n", $content);
    }

    /**
     * 处理图片URL
     * @param string $imageUrl 图片URL
     * @return string
     */
    protected function processImage($imageUrl)
    {
        if (empty($imageUrl)) {
            return '';
        }
        
        // 如果是相对路径，添加域名
        if (!preg_match('/^https?:\/\//', $imageUrl)) {
            $baseUrl = $this->config['image_base_url'] ?? '';
            if (!empty($baseUrl)) {
                $imageUrl = rtrim($baseUrl, '/') . '/' . ltrim($imageUrl, '/');
            }
        }
        
        return $imageUrl;
    }

    /**
     * 处理图片数组
     * @param array $images 图片数组
     * @return string
     */
    protected function processImages($images)
    {
        if (empty($images)) {
            return '';
        }
        
        $processedImages = [];
        foreach ($images as $image) {
            $processedImage = $this->processImage($image);
            if (!empty($processedImage)) {
                $processedImages[] = $processedImage;
            }
        }
        
        return implode(',', $processedImages);
    }

    /**
     * 格式化价格
     * @param mixed $price 价格
     * @return float
     */
    protected function formatPrice($price)
    {
        $price = floatval($price);

        // 价格范围检查 - decimal(10,2) unsigned 的范围是 0.00 到 99999999.99
        if ($price < 0) {
            $price = 0.00;
        } elseif ($price > 99999999.99) {
            $price = 99999999.99;
        }

        return round($price, 2);
    }

    /**
     * 格式化坐标
     * @param mixed $coordinate 坐标
     * @return float
     */
    protected function formatCoordinate($coordinate)
    {
        return round(floatval($coordinate), 6);
    }

    /**
     * 格式化时间
     * @param mixed $time 时间
     * @return int
     */
    protected function formatTime($time)
    {
        if (is_numeric($time)) {
            return intval($time);
        }
        
        if (is_string($time)) {
            return strtotime($time);
        }
        
        return time();
    }

    /**
     * 转换混合商品SKU数据
     * @param array $skuData 银枣混合商品数据
     * @param int $localGoodsId 本地商品ID
     * @return array
     */
    public function transformSku($skuData, $localGoodsId)
    {
        $transformed = [
            // 关联商品ID
            'goods_id' => $localGoodsId,

            // 第三方追踪 - 使用goods_main的goods_id
            'third_party_sku_id' => $skuData['goods_id'] ?? '',
            'third_party_data' => json_encode($skuData, JSON_UNESCAPED_UNICODE),

            // SKU基本信息 - 使用混合数据的最终值
            'thumbnail' => $this->processImage($skuData['thumb'] ?? ''),
            'difference' => $this->buildHybridSkuDifference($skuData),
            'price' => $this->formatPrice($skuData['final_price'] ?? $skuData['goods_price'] ?? 0),
            'market_price' => $this->formatPrice($skuData['goods_purchase_price'] ?? 0),
            'stock' => $this->extractHybridStockFromInzayun($skuData),
            'weigh' => $skuData['final_weight'] ?? $skuData['goods_weight'] ?? '0',
            'sn' => $skuData['goods_code'] ?? $skuData['barcode_code'] ?? '',
            'sales' => intval($skuData['final_sales'] ?? $skuData['goods_sales'] ?? 0),

            // 系统字段
            'last_sync_time' => time(),
            'createtime' => time(),
            'updatetime' => time(),
            'status' => 'normal'
        ];

        return $this->filterEmptyFields($transformed);
    }

    /**
     * 转换SPU数据
     * @param array $spuData 银枣SPU数据
     * @param int $localGoodsId 本地商品ID
     * @return array
     */
    public function transformSpu($spuData, $localGoodsId)
    {
        $transformed = [
            // 关联商品ID
            'goods_id' => $localGoodsId,

            // 第三方追踪
            'third_party_spu_id' => $spuData['spu_id'] ?? $spuData['goods_id'] ?? '',
            'third_party_data' => json_encode($spuData, JSON_UNESCAPED_UNICODE),

            // SPU基本信息 - 使用本地系统字段
            'name' => $spuData['spec_name'] ?? $this->extractSpecName($spuData),
            'item' => $this->buildSpuItems($spuData),

            // 系统字段
            'last_sync_time' => time(),
            'createtime' => time(),
            'updatetime' => time(),
            'status' => 'normal'
        ];

        return $this->filterEmptyFields($transformed);
    }

    /**
     * 构建SKU差异信息
     * @param array $skuData SKU数据
     * @return string
     */
    protected function buildSkuDifference($skuData)
    {
        $differences = [];

        // 从SKU数据中提取规格信息
        if (!empty($skuData['spec_info'])) {
            // 如果有规格信息字段
            $specInfo = is_string($skuData['spec_info']) ?
                json_decode($skuData['spec_info'], true) : $skuData['spec_info'];

            if (is_array($specInfo)) {
                foreach ($specInfo as $spec) {
                    if (isset($spec['name']) && isset($spec['value'])) {
                        $differences[] = $spec['name'] . ':' . $spec['value'];
                    }
                }
            }
        }

        // 从其他可能的字段中提取规格
        $specFields = ['color', 'size', 'style', 'model', 'specification'];
        foreach ($specFields as $field) {
            if (!empty($skuData[$field])) {
                $differences[] = $field . ':' . $skuData[$field];
            }
        }

        // 如果没有找到规格信息，使用默认值
        if (empty($differences)) {
            $differences[] = '默认规格';
        }

        return implode(',', $differences);
    }

    /**
     * 构建SPU项目信息（单规格商品）
     * @param array $spuData SPU数据
     * @return string
     */
    protected function buildSpuItems($spuData)
    {
        // 银枣系统是单规格的，直接返回商品名称作为规格项目
        // 不使用JSON数组格式，避免前端显示异常

        // 从SPU数据中提取规格项目
        if (!empty($spuData['spec_items']) && is_array($spuData['spec_items'])) {
            // 如果是数组，取第一个元素作为规格名称
            return $spuData['spec_items'][0] ?? '标准规格';
        } else {
            // 使用商品名称作为默认规格项目
            return $spuData['goods_name'] ?? '标准规格';
        }
    }

    /**
     * 提取规格名称（单规格商品）
     * @param array $spuData SPU数据
     * @return string
     */
    protected function extractSpecName($spuData)
    {
        // 银枣系统是单规格的，统一使用"规格"作为规格名称
        return '规格';
    }

    /**
     * 从银枣数据中提取库存
     * @param array $skuData SKU数据
     * @return int
     */
    protected function extractStockFromInzayun($skuData)
    {
        // 银枣数据库中的库存字段优先级顺序
        $stockFields = [
            // 来自inventory_main表的字段（最准确）
            'usable_xnum_sum',  // 实际可出库数量
            'xnum_sum',         // 库存总量

            // 来自goods_main表的字段
            'stock_high',       // 库存高位
            'stock_low',        // 库存低位

            // 其他可能的库存字段
            'goods_stock',      // 商品库存
            'sku_stock',        // SKU库存
            'stock',            // 库存
            'inventory',        // 库存
            'qty',              // 数量
            'quantity',         // 数量
            'num',              // 数量
            'goods_num',        // 商品数量
            'available_stock'   // 可用库存
        ];

        // 尝试从不同的库存字段中获取数据
        $availableStockFields = ['stock', 'sku_stock', 'goods_stock', 'inventory', 'quantity', 'qty'];
        
        // 查找第一个有效的库存字段
        foreach ($availableStockFields as $field) {
            if (isset($skuData[$field]) && is_numeric($skuData[$field])) {
                $stock = intval($skuData[$field]);
                $finalStock = max(0, $stock); // 确保库存不为负数
                return $finalStock;
            }
        }
        
        // 如果找不到有效的库存字段，返回默认值
        return 999; // 默认库存
    }

    /**
     * 过滤空字段
     * @param array $data 数据
     * @return array
     */
    protected function filterEmptyFields($data)
    {
        // 保留所有字段，包括空值，确保数据结构完整
        return $data;
    }

    /**
     * 构建电商商品描述
     * @param array $goodsData 电商商品数据
     * @return string
     */
    protected function buildCommerceGoodsDescription($goodsData)
    {
        $description = [];

        if (!empty($goodsData['goods_desc'])) {
            $description[] = $goodsData['goods_desc'];
        }

        if (!empty($goodsData['notesLabels'])) {
            $description[] = '规格: ' . implode(', ', $goodsData['notesLabels']);
        }

        if (!empty($goodsData['goods_unit'])) {
            $description[] = '单位: ' . $goodsData['goods_unit'];
        }

        if (!empty($goodsData['goods_weight'])) {
            $description[] = '重量: ' . $goodsData['goods_weight'];
        }

        return implode(' | ', $description);
    }

    /**
     * 构建电商商品内容
     * @param array $goodsData 电商商品数据
     * @return string
     */
    protected function buildCommerceGoodsContent($goodsData)
    {
        $content = [];

        if (!empty($goodsData['goods_content'])) {
            $content[] = $goodsData['goods_content'];
        }

        if (!empty($goodsData['goods_detail'])) {
            $content[] = $goodsData['goods_detail'];
        }

        // 添加规格标签信息
        if (!empty($goodsData['notesLabels'])) {
            $content[] = '<h3>商品规格</h3>';
            $content[] = '<ul>';
            foreach ($goodsData['notesLabels'] as $label) {
                $content[] = '<li>' . htmlspecialchars($label) . '</li>';
            }
            $content[] = '</ul>';
        }

        return implode("\n", $content);
    }

    /**
     * 构建电商商品图片
     * @param array $goodsData 电商商品数据
     * @return string
     */
    protected function buildCommerceGoodsImages($goodsData)
    {
        $images = [];

        // 主图
        if (!empty($goodsData['goods_thumb'])) {
            $images[] = $this->processImage($goodsData['goods_thumb']);
        }

        // 详情图片
        if (!empty($goodsData['picture_urls'])) {
            $pictureUrls = explode(',', $goodsData['picture_urls']);
            foreach ($pictureUrls as $url) {
                $url = trim($url);
                if ($url && !in_array($url, $images)) {
                    $images[] = $this->processImage($url);
                }
            }
        }

        return implode(',', array_filter($images));
    }

    /**
     * 映射电商商品标识
     * @param array $goodsData 电商商品数据
     * @return string
     */
    protected function mapCommerceGoodsFlag($goodsData)
    {
        $flags = [];

        // 根据销量判断是否热销
        $sales = intval($goodsData['goods_sales'] ?? 0);
        if ($sales > 100) {
            $flags[] = 'hot';
        }

        // 根据价格判断是否推荐
        $price = floatval($goodsData['goods_price'] ?? 0);
        $memberPrice = floatval($goodsData['goods_member_price'] ?? 0);
        if ($memberPrice > 0 && $memberPrice < $price) {
            $flags[] = 'recommend';
        }

        // 新品判断（可以根据创建时间等判断）
        if (!empty($goodsData['is_new']) && $goodsData['is_new'] == 1) {
            $flags[] = 'new';
        }

        return implode(',', $flags);
    }

    /**
     * 构建电商商品SKU差异
     * @param array $skuData 电商商品数据
     * @return string
     */
    protected function buildCommerceSkuDifference($skuData)
    {
        $differences = [];

        if (!empty($skuData['notesLabels'])) {
            foreach ($skuData['notesLabels'] as $label) {
                $differences[] = $label;
            }
        }

        if (!empty($skuData['goods_unit'])) {
            $differences[] = $skuData['goods_unit'];
        }

        return implode('|', $differences);
    }

    /**
     * 从电商商品数据中提取库存
     * @param array $goodsData 电商商品数据
     * @return int
     */
    protected function extractCommerceStockFromInzayun($goodsData)
    {
        // 优先使用电商商品库存
        if (isset($goodsData['goods_stock'])) {
            return max(0, intval($goodsData['goods_stock']));
        }

        // 其次使用可用库存
        if (isset($goodsData['usable_xnum_sum'])) {
            return max(0, intval($goodsData['usable_xnum_sum']));
        }

        // 最后使用总库存
        if (isset($goodsData['xnum_sum'])) {
            return max(0, intval($goodsData['xnum_sum']));
        }

        // 默认库存
        return 999;
    }

    /**
     * 计算电商商品库存
     * @param array $goodsData 电商商品数据
     * @return int
     */
    protected function calculateCommerceStock($goodsData)
    {
        return $this->extractCommerceStockFromInzayun($goodsData);
    }

    /**
     * 构建混合商品描述（简短描述，用于列表展示）
     * @param array $goodsData 混合商品数据
     * @return string
     */
    protected function buildHybridGoodsDescription($goodsData)
    {
        // description字段应该是简短描述，不使用富文本详情
        $description = [];

        // 商品简短描述
        if (!empty($goodsData['goods_desc'])) {
            $description[] = $goodsData['goods_desc'];
        }

        // 品牌信息
        if (!empty($goodsData['brand_name'])) {
            $description[] = '品牌: ' . $goodsData['brand_name'];
        }

        // 规格信息
        if (!empty($goodsData['goods_size'])) {
            $description[] = '规格: ' . $goodsData['goods_size'];
        }

        // 单位信息
        if (!empty($goodsData['final_unit'])) {
            $description[] = '单位: ' . $goodsData['final_unit'];
        }

        // 电商标签信息（如果有）
        if (!empty($goodsData['commerce_notes_labels'])) {
            $labels = explode(',', $goodsData['commerce_notes_labels']);
            $description[] = '标签: ' . implode(', ', array_slice($labels, 0, 3)); // 最多显示3个标签
        }

        // 如果没有任何描述信息，使用商品名称作为描述
        if (empty($description) && !empty($goodsData['goods_name'])) {
            $description[] = $goodsData['goods_name'];
        }

        return implode(' | ', $description);
    }

    /**
     * 构建混合商品内容
     * @param array $goodsData 混合商品数据
     * @return string
     */
    protected function buildHybridGoodsContent($goodsData)
    {
        // 优先使用电商商品详情（富文本）
        if (!empty($goodsData['commerce_goods_details'])) {
            $richTextContent = $this->processCommerceGoodsDetails($goodsData['commerce_goods_details']);
            if (!empty($richTextContent)) {
                return $richTextContent;
            }
        }

        // 回退到简单内容构建
        $content = [];

        if (!empty($goodsData['goods_content'])) {
            $content[] = $goodsData['goods_content'];
        }

        if (!empty($goodsData['goods_detail'])) {
            $content[] = $goodsData['goods_detail'];
        }

        // 添加规格标签信息
        if (!empty($goodsData['notesLabels'])) {
            $content[] = '<h3>商品规格</h3>';
            $content[] = '<ul>';
            foreach ($goodsData['notesLabels'] as $label) {
                $content[] = '<li>' . htmlspecialchars($label) . '</li>';
            }
            $content[] = '</ul>';
        }

        // 添加基本信息
        if (!empty($goodsData['goods_code'])) {
            $content[] = '<p>商品编码: ' . htmlspecialchars($goodsData['goods_code']) . '</p>';
        }

        if (!empty($goodsData['barcode_code'])) {
            $content[] = '<p>商品条码: ' . htmlspecialchars($goodsData['barcode_code']) . '</p>';
        }

        return implode("\n", $content);
    }

    /**
     * 构建混合商品图片
     * @param array $goodsData 混合商品数据
     * @return string
     */
    protected function buildHybridGoodsImages($goodsData)
    {
        $images = [];

        // 主图 - 过滤默认图片
        if (!empty($goodsData['thumb']) && !$this->isDefaultImage($goodsData['thumb'])) {
            $images[] = $this->processImage($goodsData['thumb']);
        }

        // 详情图片 - 过滤默认图片
        if (!empty($goodsData['picture_urls'])) {
            $pictureUrls = explode(',', $goodsData['picture_urls']);
            foreach ($pictureUrls as $url) {
                $url = trim($url);
                if ($url && !$this->isDefaultImage($url) && !in_array($url, $images)) {
                    $images[] = $this->processImage($url);
                }
            }
        }

        return implode(',', array_filter($images));
    }

    /**
     * 映射混合商品标识
     * @param array $goodsData 混合商品数据
     * @return string
     */
    protected function mapHybridGoodsFlag($goodsData)
    {
        $flags = [];

        // 根据销量判断是否热销
        $sales = intval($goodsData['final_sales'] ?? 0);
        if ($sales > 100) {
            $flags[] = 'hot';
        }

        // 根据价格判断是否推荐
        $price = floatval($goodsData['final_price'] ?? 0);
        $memberPrice = floatval($goodsData['final_member_price'] ?? 0);
        if ($memberPrice > 0 && $memberPrice < $price) {
            $flags[] = 'recommend';
        }

        // 新品判断
        if (!empty($goodsData['is_new']) && $goodsData['is_new'] == 1) {
            $flags[] = 'new';
        }

        return implode(',', $flags);
    }

    /**
     * 构建混合商品SKU差异（单规格商品）
     * @param array $skuData 混合商品数据
     * @return string
     */
    protected function buildHybridSkuDifference($skuData)
    {
        // 银枣系统是单规格的，使用商品名称作为规格
        // 这样可以清楚地标识每个SKU

        // 优先使用商品名称
        if (!empty($skuData['goods_name'])) {
            return $skuData['goods_name'];
        }

        // 备用：使用商品ID
        if (!empty($skuData['goods_id'])) {
            return '商品ID:' . $skuData['goods_id'];
        }

        // 默认规格
        return '标准规格';
    }

    /**
     * 从混合商品数据中提取库存
     * @param array $goodsData 混合商品数据
     * @return int
     */
    protected function extractHybridStockFromInzayun($goodsData)
    {
        // 优先使用最终库存
        if (isset($goodsData['final_stock'])) {
            return max(0, intval($goodsData['final_stock']));
        }

        // 其次使用可用库存
        if (isset($goodsData['usable_xnum_sum'])) {
            return max(0, intval($goodsData['usable_xnum_sum']));
        }

        // 最后使用总库存
        if (isset($goodsData['xnum_sum'])) {
            return max(0, intval($goodsData['xnum_sum']));
        }

        // 默认库存
        return 999;
    }

    /**
     * 映射银枣门店ID到本地门店ID
     * @param string $inzayunShopId 银枣门店ID
     * @param array $goodsData 商品数据
     * @return int
     */
    protected function mapInzayunShopId($inzayunShopId, $goodsData = [])
    {
        if (empty($inzayunShopId)) {
            return $this->getDefaultShopId();
        }

        // 查找对应的本地门店ID
        try {
            $shop = \think\Db::name('wanlshop_shop')
                ->where('third_party_id', $inzayunShopId)
                ->where('third_party_provider', 'inzayun')
                ->find();

            if ($shop) {
                return $shop['id'];
            }
        } catch (\Exception $e) {
            // 查询失败，使用默认值
        }

        return $this->getDefaultShopId();
    }

    /**
     * 获取默认门店ID
     * @return int
     */
    protected function getDefaultShopId()
    {
        try {
            // 查找第一个可用的门店
            $shop = \think\Db::name('wanlshop_shop')
                ->where('status', 1)
                ->order('id ASC')
                ->find();

            return $shop ? $shop['id'] : 1; // 如果没有门店，返回ID为1
        } catch (\Exception $e) {
            return 1; // 出错时返回默认ID
        }
    }

    /**
     * 处理有效图片URL
     * @param string $imageUrl 图片URL
     * @return string
     */
    protected function processValidImage($imageUrl)
    {
        if (empty($imageUrl)) {
            return '';
        }

        // 检查是否为默认图片
        if ($this->isDefaultImage($imageUrl)) {
            return '';
        }

        return $this->processImage($imageUrl);
    }

    /**
     * 检查是否为默认图片
     * @param string $imageUrl 图片URL
     * @return bool
     */
    protected function isDefaultImage($imageUrl)
    {
        if (empty($imageUrl)) {
            return true;
        }

        // 检查是否为默认图片URL
        $defaultPatterns = [
            '/default\.png$/',
            '/default\.jpg$/',
            '/default\.jpeg$/',
            '/placeholder/',
            '/no-image/',
            '/404/'
        ];

        foreach ($defaultPatterns as $pattern) {
            if (preg_match($pattern, $imageUrl)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从图片组中获取主图
     * @param string $images 图片组字符串
     * @return string
     */
    protected function getMainImageFromImages($images)
    {
        if (empty($images)) {
            return '';
        }

        // 获取第一张图片作为主图
        $imageArray = explode(',', $images);
        return trim($imageArray[0] ?? '');
    }

    /**
     * 处理电商商品详情（富文本）
     * @param string $commerceDetails HTML编码的商品详情
     * @return string
     */
    protected function processCommerceGoodsDetails($commerceDetails)
    {
        if (empty($commerceDetails)) {
            return '';
        }

        // HTML解码
        $decodedDetails = html_entity_decode($commerceDetails, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // 处理图片URL，确保图片可以正常显示
        $decodedDetails = $this->processDetailsImages($decodedDetails);

        return $decodedDetails;
    }

    /**
     * 构建品牌内容
     * @param array $brandData 品牌数据
     * @return string
     */
    protected function buildBrandContent($brandData)
    {
        $content = [];

        if (!empty($brandData['brand_name'])) {
            $content[] = "<h3>品牌名称</h3>";
            $content[] = "<p>" . htmlspecialchars($brandData['brand_name']) . "</p>";
        }

        if (!empty($brandData['brand_code'])) {
            $content[] = "<h3>品牌编码</h3>";
            $content[] = "<p>" . htmlspecialchars($brandData['brand_code']) . "</p>";
        }

        if (empty($content)) {
            $content[] = "<p>品牌介绍</p>";
        }

        return implode("\n", $content);
    }

    /**
     * 映射品牌状态
     * @param int $status 银枣品牌状态
     * @return string
     */
    protected function mapBrandStatus($status)
    {
        return intval($status) === 1 ? 'normal' : 'hidden';
    }

    /**
     * 映射品牌状态值
     * @param int $status 银枣品牌状态
     * @return string
     */
    protected function mapBrandState($status)
    {
        return intval($status) === 1 ? '1' : '0';
    }

    /**
     * 映射品牌开关
     * @param int $status 银枣品牌状态
     * @return int
     */
    protected function mapBrandSwitch($status)
    {
        return intval($status) === 1 ? 1 : 0;
    }

    /**
     * 处理详情中的图片URL
     * @param string $details 详情HTML内容
     * @return string
     */
    protected function processDetailsImages($details)
    {
        // 处理图片标签，确保src属性正确
        $details = preg_replace_callback(
            '/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i',
            function($matches) {
                $imgTag = $matches[0];
                $srcUrl = $matches[1];

                // 确保图片URL是完整的
                if (!preg_match('/^https?:\/\//', $srcUrl)) {
                    // 如果是相对路径，可以在这里处理
                    // 目前银枣的图片都是完整URL，所以暂时不处理
                }

                return $imgTag;
            },
            $details
        );

        return $details;
    }

    /**
     * 构建商品参数数据
     * @param array $goodsData 商品数据
     * @return string JSON格式的参数数据
     */
    protected function buildGoodsParameter($goodsData)
    {
        $parameter = [];

        // 提取品牌信息
        $brandName = '';
        if (!empty($goodsData['brand_name'])) {
            $brandName = $goodsData['brand_name'];
        } elseif (!empty($goodsData['goods_brand'])) {
            $brandName = $goodsData['goods_brand'];
        } elseif (!empty($goodsData['brand'])) {
            $brandName = $goodsData['brand'];
        }

        // 如果有品牌信息，添加到参数中
        if (!empty($brandName)) {
            $parameter['品牌'] = $brandName;
        }

        // 提取规格信息
        if (!empty($goodsData['goods_size'])) {
            $parameter['规格'] = $goodsData['goods_size'];
        } elseif (!empty($goodsData['size'])) {
            $parameter['规格'] = $goodsData['size'];
        }

        // 提取单位信息
        if (!empty($goodsData['unit'])) {
            $parameter['单位'] = $goodsData['unit'];
        }

        // 如果没有任何参数，至少提供一个默认品牌
        if (empty($parameter)) {
            $parameter['品牌'] = '暂无';
        }

        return json_encode($parameter, JSON_UNESCAPED_UNICODE);
    }
}
