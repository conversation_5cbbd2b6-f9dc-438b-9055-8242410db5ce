<?php
/**
 * 调试数据库连接和查询差异
 */

// 设置环境变量
define('APP_PATH', __DIR__ . '/application/');
define('RUNTIME_PATH', __DIR__ . '/runtime/');

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/base.php';

// 初始化应用
\think\App::initCommon();

echo "=== 数据库连接调试 ===\n\n";

$testMemberId = 31828; // 使用你提到的会员ID

try {
    echo "1. 测试线上数据库直连（使用dshop用户）...\n";
    
    // 使用配置文件中的连接信息
    $onlinePdo = new PDO(
        'mysql:host=**************;port=3306;dbname=dshop;charset=utf8mb4',
        'dshop',
        '53iLcNZ45spjcTNG',
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "   ✅ 线上数据库连接成功（dshop用户）\n";
    
    // 测试查询
    $sql = "SELECT member_id, member_mobile, member_points, is_del FROM trader_member WHERE member_id = ? AND is_del = 0";
    $stmt = $onlinePdo->prepare($sql);
    $stmt->execute([$testMemberId]);
    $result1 = $stmt->fetchAll();
    
    echo "   查询结果（dshop用户）:\n";
    echo "     SQL: {$sql}\n";
    echo "     参数: [{$testMemberId}]\n";
    echo "     结果数量: " . count($result1) . "\n";
    echo "     结果内容: " . json_encode($result1, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "2. 测试不带is_del条件的查询...\n";
    
    $sql2 = "SELECT member_id, member_mobile, member_points, is_del FROM trader_member WHERE member_id = ?";
    $stmt2 = $onlinePdo->prepare($sql2);
    $stmt2->execute([$testMemberId]);
    $result2 = $stmt2->fetchAll();
    
    echo "   查询结果（不带is_del条件）:\n";
    echo "     SQL: {$sql2}\n";
    echo "     参数: [{$testMemberId}]\n";
    echo "     结果数量: " . count($result2) . "\n";
    echo "     结果内容: " . json_encode($result2, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "3. 测试DatabaseManager查询...\n";
    
    // 使用DatabaseManager查询
    \inzayun\DatabaseManager::init();
    
    $sql3 = "SELECT member_points FROM trader_member WHERE member_id = ? AND is_del = 0";
    $result3 = \inzayun\DatabaseManager::query($sql3, [$testMemberId]);
    
    echo "   DatabaseManager查询结果:\n";
    echo "     SQL: {$sql3}\n";
    echo "     参数: [{$testMemberId}]\n";
    echo "     结果数量: " . (is_array($result3) ? count($result3) : 0) . "\n";
    echo "     结果内容: " . json_encode($result3, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "4. 测试表结构...\n";
    
    $stmt4 = $onlinePdo->query("DESCRIBE trader_member");
    $columns = $stmt4->fetchAll();
    
    echo "   trader_member表结构:\n";
    foreach ($columns as $column) {
        if (in_array($column['Field'], ['member_id', 'member_points', 'is_del'])) {
            echo "     {$column['Field']}: {$column['Type']} (Null: {$column['Null']}, Default: {$column['Default']})\n";
        }
    }
    echo "\n";
    
    echo "5. 检查is_del字段的值分布...\n";
    
    $stmt5 = $onlinePdo->query("SELECT is_del, COUNT(*) as count FROM trader_member GROUP BY is_del");
    $isDelStats = $stmt5->fetchAll();
    
    echo "   is_del字段值分布:\n";
    foreach ($isDelStats as $stat) {
        echo "     is_del = {$stat['is_del']}: {$stat['count']} 条记录\n";
    }
    echo "\n";
    
    echo "6. 检查特定会员的is_del值...\n";
    
    $stmt6 = $onlinePdo->prepare("SELECT member_id, is_del, member_points FROM trader_member WHERE member_id = ?");
    $stmt6->execute([$testMemberId]);
    $memberInfo = $stmt6->fetch();
    
    if ($memberInfo) {
        echo "   会员ID {$testMemberId} 的信息:\n";
        echo "     is_del: {$memberInfo['is_del']}\n";
        echo "     member_points: {$memberInfo['member_points']}\n";
    } else {
        echo "   ❌ 未找到会员ID {$testMemberId}\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    
    // 分析结果
    echo "\n=== 结果分析 ===\n";
    
    if (empty($result1) && !empty($result2)) {
        echo "❌ 问题：is_del条件导致查询无结果\n";
        echo "   建议：检查会员ID {$testMemberId} 的is_del字段值\n";
    } elseif (empty($result1) && empty($result2)) {
        echo "❌ 问题：会员ID {$testMemberId} 不存在\n";
    } elseif (!empty($result1) && empty($result3)) {
        echo "❌ 问题：DatabaseManager查询失败\n";
        echo "   建议：检查DatabaseManager的连接配置或权限\n";
    } elseif (!empty($result1) && !empty($result3)) {
        echo "✅ 查询正常，可能是其他问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
    echo "   文件: " . $e->getFile() . "\n";
    echo "   行号: " . $e->getLine() . "\n";
}
